#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import csv
from datetime import datetime

class MaxaDuplicateChecker:
    def __init__(self):
        self.db_config = {
            'user': 'root',
            'password': 'root',
            'host': 'localhost',
            'port': '3306',
            'database': 'urbox'
        }
    
    def execute_query(self, query):
        """Thực thi query MySQL và trả về kết quả"""
        try:
            cmd = [
                'mysql', '-u', self.db_config['user'], 
                f"-p{self.db_config['password']}", 
                '-h', self.db_config['host'], 
                '-P', self.db_config['port'],
                '-D', self.db_config['database'], 
                '-e', query
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Lỗi query: {result.stderr}")
                return None
            
            return result.stdout.strip()
            
        except Exception as e:
            print(f"❌ Exception execute query: {e}")
            return None
    
    def parse_query_result(self, result_text):
        """Parse kết quả query thành list of dict"""
        if not result_text:
            return []
        
        lines = result_text.split('\n')
        if len(lines) < 2:
            return []
        
        # Lấy header
        headers = lines[0].split('\t')
        
        # Parse data
        data = []
        for line in lines[1:]:
            if line.strip():
                values = line.split('\t')
                if len(values) == len(headers):
                    row = {}
                    for i, header in enumerate(headers):
                        row[header] = values[i] if values[i] != 'NULL' else None
                    data.append(row)
        
        return data
    
    def get_maxa_statistics(self):
        """Lấy thống kê về maxa"""
        print("📊 Thống kê tổng quan về maxa...")
        
        queries = {
            'total_records': "SELECT COUNT(*) as count FROM xaphuong",
            'records_with_maxa': "SELECT COUNT(*) as count FROM xaphuong WHERE maxa IS NOT NULL",
            'records_without_maxa': "SELECT COUNT(*) as count FROM xaphuong WHERE maxa IS NULL",
            'unique_maxa_count': "SELECT COUNT(DISTINCT maxa) as count FROM xaphuong WHERE maxa IS NOT NULL",
            'min_maxa': "SELECT MIN(maxa) as value FROM xaphuong WHERE maxa IS NOT NULL",
            'max_maxa': "SELECT MAX(maxa) as value FROM xaphuong WHERE maxa IS NOT NULL"
        }
        
        stats = {}
        for key, query in queries.items():
            result = self.execute_query(query)
            if result:
                data = self.parse_query_result(result)
                if data:
                    stats[key] = data[0]['count'] if 'count' in data[0] else data[0]['value']
        
        return stats
    
    def find_duplicate_maxa(self):
        """Tìm các maxa bị trùng lặp"""
        print("🔍 Tìm các maxa bị trùng lặp...")
        
        query = """
        SELECT 
            maxa, 
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(id ORDER BY id) as record_ids,
            GROUP_CONCAT(CONCAT(id, ':', tenhc) ORDER BY id SEPARATOR ' | ') as records_info
        FROM xaphuong 
        WHERE maxa IS NOT NULL 
        GROUP BY maxa 
        HAVING COUNT(*) > 1 
        ORDER BY duplicate_count DESC, maxa
        """
        
        result = self.execute_query(query)
        if result:
            return self.parse_query_result(result)
        return []
    
    def get_duplicate_details(self, maxa):
        """Lấy thông tin chi tiết của các record có cùng maxa"""
        query = f"""
        SELECT 
            id, matinh, ma, tenhc, maxa,
            (SELECT COUNT(*) FROM ward_geometry WHERE pti_id = xaphuong.maxa) as has_geometry
        FROM xaphuong 
        WHERE maxa = {maxa}
        ORDER BY id
        """
        
        result = self.execute_query(query)
        if result:
            return self.parse_query_result(result)
        return []
    
    def analyze_duplicate_patterns(self, duplicates):
        """Phân tích pattern của các trùng lặp"""
        print("🔬 Phân tích pattern trùng lặp...")
        
        analysis = {
            'total_duplicate_maxa': len(duplicates),
            'total_affected_records': 0,
            'maxa_with_null_value': 0,
            'maxa_with_negative_value': 0,
            'maxa_with_positive_value': 0,
            'max_duplicate_count': 0
        }
        
        for dup in duplicates:
            maxa = dup['maxa']
            count = int(dup['duplicate_count'])
            
            analysis['total_affected_records'] += count
            
            if maxa is None or maxa == 'NULL':
                analysis['maxa_with_null_value'] += 1
            elif int(maxa) < 0:
                analysis['maxa_with_negative_value'] += 1
            else:
                analysis['maxa_with_positive_value'] += 1
            
            if count > analysis['max_duplicate_count']:
                analysis['max_duplicate_count'] = count
        
        return analysis
    
    def export_duplicate_maxa_csv(self, duplicates, filename):
        """Xuất báo cáo trùng lặp maxa ra CSV"""
        print(f"📄 Xuất báo cáo trùng lặp maxa ra file: {filename}")
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'maxa', 'duplicate_count', 'record_id', 'matinh', 'ma', 
                    'tenhc', 'has_geometry', 'note'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                # Ghi header
                writer.writeheader()
                
                # Ghi dữ liệu cho từng maxa trùng lặp
                for dup in duplicates:
                    maxa = dup['maxa']
                    duplicate_count = dup['duplicate_count']
                    
                    # Lấy chi tiết các record
                    details = self.get_duplicate_details(maxa)
                    
                    for i, detail in enumerate(details):
                        note = ""
                        if maxa == '-1':
                            note = "Maxa = -1 (có thể là dữ liệu chưa được gán maxa)"
                        elif i == 0:
                            note = "Record đầu tiên trong nhóm trùng lặp"
                        else:
                            note = f"Record trùng lặp thứ {i+1}"
                        
                        writer.writerow({
                            'maxa': maxa,
                            'duplicate_count': duplicate_count,
                            'record_id': detail['id'],
                            'matinh': detail['matinh'],
                            'ma': detail['ma'],
                            'tenhc': detail['tenhc'],
                            'has_geometry': 'Yes' if int(detail['has_geometry']) > 0 else 'No',
                            'note': note
                        })
            
            print(f"✅ Đã xuất thành công ra file: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi xuất file CSV: {e}")
            return False
    
    def run_maxa_duplicate_check(self):
        """Chạy kiểm tra trùng lặp maxa toàn bộ"""
        print("🚀 BẮT ĐẦU KIỂM TRA TRÙNG LẶP MAXA")
        print("=" * 60)
        
        # Thống kê tổng quan
        stats = self.get_maxa_statistics()
        print("📊 THỐNG KÊ TỔNG QUAN:")
        print(f"   - Tổng số records: {stats.get('total_records', 'N/A')}")
        print(f"   - Records có maxa: {stats.get('records_with_maxa', 'N/A')}")
        print(f"   - Records không có maxa: {stats.get('records_without_maxa', 'N/A')}")
        print(f"   - Số maxa unique: {stats.get('unique_maxa_count', 'N/A')}")
        print(f"   - Maxa nhỏ nhất: {stats.get('min_maxa', 'N/A')}")
        print(f"   - Maxa lớn nhất: {stats.get('max_maxa', 'N/A')}")
        print()
        
        # Tìm trùng lặp
        duplicates = self.find_duplicate_maxa()
        
        if not duplicates:
            print("✅ Không tìm thấy maxa nào bị trùng lặp!")
            return []
        
        # Phân tích pattern
        analysis = self.analyze_duplicate_patterns(duplicates)
        
        print("🔬 PHÂN TÍCH TRÙNG LẶP:")
        print(f"   - Số maxa bị trùng lặp: {analysis['total_duplicate_maxa']}")
        print(f"   - Tổng records bị ảnh hưởng: {analysis['total_affected_records']}")
        print(f"   - Maxa = NULL: {analysis['maxa_with_null_value']}")
        print(f"   - Maxa âm: {analysis['maxa_with_negative_value']}")
        print(f"   - Maxa dương: {analysis['maxa_with_positive_value']}")
        print(f"   - Số trùng lặp cao nhất: {analysis['max_duplicate_count']}")
        print()
        
        # Hiển thị chi tiết từng case
        print("📋 CHI TIẾT CÁC CASE TRÙNG LẶP:")
        print("-" * 60)
        for i, dup in enumerate(duplicates, 1):
            maxa = dup['maxa']
            count = dup['duplicate_count']
            print(f"{i}. Maxa = {maxa} (trùng {count} lần)")
            
            details = self.get_duplicate_details(maxa)
            for detail in details:
                geometry_status = "✅" if int(detail['has_geometry']) > 0 else "❌"
                print(f"   - ID {detail['id']}: {detail['tenhc']} (matinh={detail['matinh']}, ma={detail['ma']}) {geometry_status}")
            print()
        
        # Xuất CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"duplicate_maxa_{timestamp}.csv"
        
        if self.export_duplicate_maxa_csv(duplicates, filename):
            print(f"✅ Đã xuất báo cáo chi tiết ra file: {filename}")
        
        return duplicates

def main():
    """Hàm main"""
    print("🔧 MAXA DUPLICATE CHECKER TOOL")
    print("=" * 60)
    
    checker = MaxaDuplicateChecker()
    duplicates = checker.run_maxa_duplicate_check()
    
    if duplicates:
        print(f"\n⚠️ Tìm thấy {len(duplicates)} maxa bị trùng lặp cần xử lý!")
        print("\n💡 GỢI Ý XỬ LÝ:")
        print("   - Kiểm tra các record có maxa = -1 (có thể cần gán maxa mới)")
        print("   - Xem xét merge hoặc xóa các record trùng lặp")
        print("   - Cập nhật dữ liệu geometry cho các record chưa có")
    else:
        print("\n🎉 Dữ liệu maxa sạch, không có trùng lặp!")

if __name__ == "__main__":
    main()
