# Tool lấy lat/long cho brand_office bằng Google Maps API

## 1. <PERSON><PERSON><PERSON> đích
- L<PERSON>y tọa độ (latitude, longitude) cho các bản ghi trong bảng `brand_office` dựa trên trường `address_old`.
- Xuất ra file SQL update và log lỗi các địa chỉ không lấy được tọa độ.

## 2. File liên quan
- `get_latlong_brand_office.py`: Script lấy lat/long từ Google Maps API, xuất file SQL update và file log lỗi.
- `run_sql_update.py`: Script chạy file SQL update vào MySQL.
- `update_brand_office_latlong.sql`: File SQL update lat/long (tự động sinh ra).
- `brand_office_latlong_error.csv`: File log lỗi địa chỉ (tự động sinh ra).

## 3. Cách sử dụng

### Bước 1: Cài đặt thư viện
```bash
pip install requests
```

### Bước 2: Lấy Google Maps API Key
- Đ<PERSON><PERSON> ký tại https://console.cloud.google.com/
- Bật API: Geocoding API
- Lấy API key và điền vào biến `API_KEY` trong file `get_latlong_brand_office.py`

### Bước 3: Chạy script lấy lat/long
```bash
python get_latlong_brand_office.py
```
- Script sẽ lấy tối đa 1000 bản ghi/lần (có thể sửa biến `MAX_REQUESTS` nếu cần)
- Kết quả:
  - File `update_brand_office_latlong.sql` chứa các câu lệnh UPDATE
  - File `brand_office_latlong_error.csv` chứa các địa chỉ lỗi (không lấy được tọa độ)

### Bước 4: Chạy file SQL update vào MySQL
```bash
python run_sql_update.py
```
- Hoặc tự chạy thủ công:
```bash
mysql -u root -proot -h localhost -P 3306 -D urbox < update_brand_office_latlong.sql
```

## 4. Lưu ý
- Nên kiểm tra lại file log lỗi để xử lý thủ công các địa chỉ không lấy được tọa độ.
- Google Maps API có quota miễn phí và giới hạn request/ngày. Nếu bị lỗi quota, hãy thử lại sau hoặc nâng cấp tài khoản.
- Có thể chỉnh sửa thông tin kết nối DB trong 2 script nếu cần.

## 5. Liên hệ
- Nếu cần hỗ trợ, liên hệ team phát triển dự án sapnhap. 