# CONFLUENCE CONTENT TEMPLATES
## Dự án <PERSON>p nhật Hệ thống Hành chính Việt Nam

---

## PAGE STRUCTURE

### Parent Page: "Dự án Hệ thống Hành chính VN"
- Overview và navigation
- Links đến tất cả sub-pages
- Quick stats và timeline

### Sub-pages:
1. **00 - Dashboard Tổng quan**
2. **01 - <PERSON><PERSON>o cáo PM Executive** 
3. **02 - Báo cáo Business Analysis**
4. **03 - Báo cáo Technical Development**
5. **04 - Task Breakdown Chi tiết**

---

## CONFLUENCE API ENDPOINTS NEEDED

```
POST /wiki/rest/api/content
- Tạo page mới

PUT /wiki/rest/api/content/{id}
- Update page content

GET /wiki/rest/api/space/{spaceKey}
- Get space info

POST /wiki/rest/api/content/{id}/child/page
- Tạo child page
```

---

## CONTENT FORMATTING FOR CONFLUENCE

### Confluence Storage Format (XML-like):
```xml
<ac:structured-macro ac:name="info">
  <ac:rich-text-body>
    <p>This is an info panel</p>
  </ac:rich-text-body>
</ac:structured-macro>
```

### Tables:
```xml
<table>
  <tbody>
    <tr>
      <th>Header 1</th>
      <th>Header 2</th>
    </tr>
    <tr>
      <td>Data 1</td>
      <td>Data 2</td>
    </tr>
  </tbody>
</table>
```

### Code Blocks:
```xml
<ac:structured-macro ac:name="code">
  <ac:parameter ac:name="language">sql</ac:parameter>
  <ac:plain-text-body>
    SELECT * FROM table;
  </ac:plain-text-body>
</ac:structured-macro>
```

---

## NAVIGATION STRUCTURE

### Breadcrumb:
Home > Projects > Hệ thống Hành chính VN > [Current Page]

### Page Links:
- Previous/Next navigation
- Table of contents macro
- Related pages section

---

## READY TO IMPLEMENT

Khi có Confluence space info:
1. Tạo parent page với overview
2. Tạo 5 sub-pages với content từ báo cáo
3. Setup navigation links
4. Add links vào Jira UBG-2792
5. Version control setup

---

## CONFLUENCE SPACE INFO NEEDED

- Space Key (ví dụ: PROJ, DEV, DOCS)
- Space URL
- Parent page name (optional)
- Permissions level
