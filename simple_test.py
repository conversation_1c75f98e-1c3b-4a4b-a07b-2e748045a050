#!/usr/bin/env python3

import os
import glob

# Test file detection
geometry_files = [
    "crawl_geometry.py",
    "crawl_single_geometry.py", 
    "crawl_missing_geometry.py",
    "create_ward_geometry_table.sql",
    "sample_geometry.json"
]

print("🔍 Checking geometry crawl files:")
existing_files = []
for file_path in geometry_files:
    if os.path.exists(file_path):
        existing_files.append(file_path)
        file_size = os.path.getsize(file_path)
        print(f"✅ Found: {file_path} ({file_size:,} bytes)")
    else:
        print(f"❌ Missing: {file_path}")

print(f"\n📊 Summary: {len(existing_files)} files found")

if existing_files:
    print("\n📋 Files ready for upload:")
    for f in existing_files:
        print(f"  - {f}")
else:
    print("\n❌ No files to upload!")
