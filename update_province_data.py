#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import unicodedata
import time

def create_slug(text):
    """Tạo slug từ text tiếng Việt - loại bỏ prefix Tỉnh/Thành phố, giữ tất cả chữ cái, chỉ bỏ dấu"""
    # Loại bỏ prefix "Tỉnh " hoặc "Thành phố "
    text = re.sub(r'^(Tỉnh|Thành phố)\s+', '', text, flags=re.IGNORECASE)

    # Mapping các ký tự đặc biệt tiếng Việt
    char_map = {
        'đ': 'd', 'Đ': 'd',
        'ô': 'o', 'Ô': 'o',
        'ơ': 'o', 'Ơ': 'o',
        'ư': 'u', 'Ư': 'u',
        'â': 'a', 'Â': 'a',
        'ê': 'e', 'Ê': 'e',
        'ă': 'a', 'Ă': 'a'
    }

    # Thay thế các ký tự đặc biệt trước
    for vn_char, en_char in char_map.items():
        text = text.replace(vn_char, en_char)

    # Loại bỏ dấu tiếng Việt
    text = unicodedata.normalize('NFD', text)
    text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')

    # Chuyển thành lowercase
    text = text.lower()

    # Chỉ thay thế khoảng trắng và dấu câu bằng dấu gạch ngang, giữ lại tất cả chữ cái và số
    text = re.sub(r'[^a-z0-9]+', '-', text)

    # Loại bỏ dấu gạch ngang ở đầu và cuối
    text = text.strip('-')

    return text

def get_provinces_data():
    """Lấy danh sách tỉnh/thành phố từ database query trước đó"""
    provinces = [
        (114, "Thành phố Cần Thơ"),
        (82, "Thành phố Hà Nội"), 
        (94, "Thành phố Hải Phòng"),
        (109, "Thành phố Hồ Chí Minh"),
        (101, "Thành phố Huế"),
        (102, "Thành phố Đà Nẵng"),
        (113, "Tỉnh An Giang"),
        (92, "Tỉnh Bắc Ninh"),
        (115, "Tỉnh Cà Mau"),
        (83, "Tỉnh Cao Bằng"),
        (104, "Tỉnh Gia Lai"),
        (99, "Tỉnh Hà Tĩnh"),
        (95, "Tỉnh Hưng Yên"),
        (105, "Tỉnh Khánh Hòa"),
        (86, "Tỉnh Lai Châu"),
        (107, "Tỉnh Lâm Đồng"),
        (90, "Tỉnh Lạng Sơn"),
        (88, "Tỉnh Lào Cai"),
        (98, "Tỉnh Nghệ An"),
        (96, "Tỉnh Ninh Bình"),
        (93, "Tỉnh Phú Thọ"),
        (103, "Tỉnh Quảng Ngãi"),
        (91, "Tỉnh Quảng Ninh"),
        (100, "Tỉnh Quảng Trị"),
        (87, "Tỉnh Sơn La"),
        (110, "Tỉnh Tây Ninh"),
        (89, "Tỉnh Thái Nguyên"),
        (97, "Tỉnh Thanh Hóa"),
        (84, "Tỉnh Tuyên Quang"),
        (112, "Tỉnh Vĩnh Long"),
        (106, "Tỉnh Đắk Lắk"),
        (85, "Tỉnh Điện Biên"),
        (108, "Tỉnh Đồng Nai"),
        (111, "Tỉnh Đồng Tháp")
    ]
    
    # Sắp xếp theo alphabet
    return sorted(provinces, key=lambda x: x[1])

def generate_update_statements():
    """Tạo các câu lệnh UPDATE"""

    print("🔄 Tạo câu lệnh UPDATE cho records có is_megre = 2...")

    provinces_data = get_provinces_data()
    current_time = int(time.time())

    # Tạo danh sách với slug để sắp xếp
    provinces_with_slug = []
    for province_id, title in provinces_data:
        safe_title = create_slug(title)
        is_city = 1 if title.startswith("Thành phố") else 0
        provinces_with_slug.append((province_id, title, safe_title, is_city))

    # Sắp xếp theo slug thay vì title
    provinces_sorted = sorted(provinces_with_slug, key=lambda x: x[2])  # Sort by slug

    print(f"📊 Sẽ cập nhật {len(provinces_sorted)} records")
    print("📋 Danh sách cập nhật (theo alphabet slug):")

    update_statements = []

    for position, (province_id, title, safe_title, is_city) in enumerate(provinces_sorted, 1):
        
        # Tạo câu lệnh UPDATE
        update_sql = f"""UPDATE ___province SET 
    is_city = {is_city},
    position = {position},
    safe_title = '{safe_title}',
    updated_at = {current_time},
    updated_by = 'update_script'
WHERE id = {province_id}"""
        
        update_statements.append(update_sql)
        
        print(f"📝 {position:2d}. ID {province_id}: {title}")
        print(f"     -> slug: {safe_title}, is_city: {is_city}")
    
    # Ghi ra file SQL backup
    with open('update_province_statements.sql', 'w', encoding='utf-8') as f:
        f.write('-- Cập nhật records có is_megre = 2\n')
        f.write('-- Cập nhật is_city từ 2->1 cho thành phố, position theo alphabet, safe_title\n\n')
        
        for i, stmt in enumerate(update_statements):
            f.write(f'-- Record {i+1}\n')
            f.write(stmt + ';\n\n')
    
    print(f"\n💾 Đã tạo file backup: update_province_statements.sql")
    print(f"🎯 Sẵn sàng thực thi {len(update_statements)} câu lệnh UPDATE")
    
    return update_statements

if __name__ == "__main__":
    generate_update_statements()
