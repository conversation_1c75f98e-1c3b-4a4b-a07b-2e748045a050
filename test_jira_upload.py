#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for Jira integration
Run this after configuring jira_config.py
"""

from jira_integration import (
    upload_geometry_files,
    upload_brand_office_files,
    upload_files,
    jira_integration
)

def test_config():
    """Test Jira configuration"""
    print("🔧 TESTING JIRA CONFIGURATION")
    print("=" * 60)
    
    try:
        from jira_config import validate_config
        is_valid, message = validate_config()
        
        if is_valid:
            print("✅ Configuration is valid")
            return True
        else:
            print(f"❌ Configuration error: {message}")
            print("\nPlease edit jira_config.py and fill in:")
            print("- email: Your Jira email")
            print("- api_token: Your Jira API token")
            return False
            
    except ImportError:
        print("❌ jira_config.py not found")
        print("Please copy jira_config_template.py to jira_config.py and configure it")
        return False

def test_file_detection():
    """Test file detection for different work types"""
    print("\n🔍 TESTING FILE DETECTION")
    print("=" * 60)
    
    work_types = ['geometry_crawl', 'brand_office_coordinates', 'jira_tools']
    
    for work_type in work_types:
        files = jira_integration.auto_detect_files(work_type)
        print(f"\n📁 {work_type}:")
        if files:
            for f in files:
                print(f"  ✅ {f}")
        else:
            print("  ❌ No files found")

def test_geometry_upload():
    """Test uploading geometry crawl files"""
    print("\n🚀 TESTING GEOMETRY UPLOAD")
    print("=" * 60)
    
    if not test_config():
        return False
    
    try:
        print("Uploading geometry crawl files...")
        result = upload_geometry_files()
        
        if result and result['task_created']:
            print(f"✅ SUCCESS! Task created: {result['task_key']}")
            return True
        else:
            print("❌ Upload failed!")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_brand_office_upload():
    """Test uploading brand office files"""
    print("\n🏢 TESTING BRAND OFFICE UPLOAD")
    print("=" * 60)
    
    if not test_config():
        return False
    
    try:
        print("Uploading brand office files...")
        result = upload_brand_office_files()
        
        if result and result['task_created']:
            print(f"✅ SUCCESS! Task created: {result['task_key']}")
            return True
        else:
            print("❌ Upload failed!")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_custom_upload():
    """Test uploading custom files"""
    print("\n📁 TESTING CUSTOM UPLOAD")
    print("=" * 60)
    
    if not test_config():
        return False
    
    # Test files (create if not exist)
    test_files = ['test_file1.txt', 'test_file2.txt']
    
    # Create test files
    for file_path in test_files:
        with open(file_path, 'w') as f:
            f.write(f"Test content for {file_path}")
        print(f"📝 Created test file: {file_path}")
    
    try:
        print("Uploading custom files...")
        result = upload_files(
            "Test Custom Upload", 
            test_files,
            "Test upload với custom files"
        )
        
        if result and result['task_created']:
            print(f"✅ SUCCESS! Task created: {result['task_key']}")
            
            # Clean up test files
            import os
            for file_path in test_files:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"🗑️ Cleaned up: {file_path}")
            
            return True
        else:
            print("❌ Upload failed!")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 JIRA INTEGRATION TEST SUITE")
    print("=" * 60)
    
    print("Available tests:")
    print("1. Test configuration")
    print("2. Test file detection")
    print("3. Test geometry upload")
    print("4. Test brand office upload")
    print("5. Test custom upload")
    print("6. Run all tests")
    
    choice = input("\nSelect test (1-6): ").strip()
    
    if choice == "1":
        test_config()
    elif choice == "2":
        test_file_detection()
    elif choice == "3":
        test_geometry_upload()
    elif choice == "4":
        test_brand_office_upload()
    elif choice == "5":
        test_custom_upload()
    elif choice == "6":
        print("🚀 Running all tests...")
        
        # Test 1: Config
        if not test_config():
            print("❌ Config test failed, stopping")
            return
        
        # Test 2: File detection
        test_file_detection()
        
        # Test 3-5: Upload tests (commented out to avoid spam)
        print("\n⚠️ Upload tests are available but not run automatically")
        print("Run individual tests (3-5) to test actual uploads")
        
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    main()
