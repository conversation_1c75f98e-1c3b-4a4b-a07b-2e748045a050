#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.request
import urllib.parse
import json
import time
import subprocess
import random
from datetime import datetime

class GeometryCrawler:
    def __init__(self):
        self.base_url = "https://sapnhap.bando.com.vn/pread_json"
        self.headers = {
            'Accept': 'text/plain, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Cookie': 'PHPSESSID=tncekeavtrlr9jtequvls32mtl',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
    def get_xaphuong_data(self):
        """Lấy danh sách xã phường từ database"""
        print("🔍 Lấy danh sách xã phường từ database...")
        
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', 
                'SELECT id, matinh, ma, tenhc, maxa FROM xaphuong WHERE maxa IS NOT NULL ORDER BY id'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Lỗi query database: {result.stderr}")
                return []
            
            # Parse kết quả
            lines = result.stdout.strip().split('\n')
            xaphuong_list = []
            
            for line in lines[1:]:  # Bỏ header
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 5 and parts[4] and parts[4] != 'NULL':
                        try:
                            xaphuong_record = {
                                'id': int(parts[0]),
                                'matinh': int(parts[1]),
                                'ma': parts[2],
                                'tenhc': parts[3],
                                'maxa': int(parts[4])
                            }
                            xaphuong_list.append(xaphuong_record)
                        except ValueError:
                            continue
            
            print(f"✅ Tìm thấy {len(xaphuong_list)} xã phường có maxa")
            return xaphuong_list
            
        except Exception as e:
            print(f"❌ Lỗi get xaphuong data: {e}")
            return []
    
    def check_existing_geometry(self, maxa):
        """Kiểm tra xem đã có geometry data cho maxa này chưa"""
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e',
                f'SELECT COUNT(*) FROM ward_geometry WHERE pti_id = {maxa}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    count = int(lines[1])
                    return count > 0
            return False

        except Exception as e:
            print(f"❌ Lỗi check existing geometry: {e}")
            return False
    
    def crawl_geometry_data(self, api_id):
        """Crawl geometry data cho một api_id (maxa + 1)"""
        # Tạo ID theo format xa3321.{api_id}
        geometry_id = f"xa3321.{api_id}"

        print(f"🔄 Crawling geometry cho ID: {geometry_id}")

        try:
            # Tạo POST data
            data = urllib.parse.urlencode({'id': geometry_id}).encode('utf-8')

            # Tạo request
            req = urllib.request.Request(
                self.base_url,
                data=data,
                headers=self.headers
            )

            # Gửi request
            with urllib.request.urlopen(req, timeout=30) as response:
                if response.getcode() != 200:
                    print(f"❌ HTTP Error: {response.getcode()}")
                    return None

                # Đọc response
                response_data = response.read().decode('utf-8')

                # Kiểm tra response có hợp lệ không
                if not response_data or response_data.strip() == '':
                    print(f"⚠️ Response rỗng cho ID: {geometry_id}")
                    return None

                # Thử parse JSON để kiểm tra tính hợp lệ
                try:
                    json.loads(response_data)
                    print(f"✅ Crawl thành công geometry cho ID: {geometry_id}")
                    return response_data
                except json.JSONDecodeError:
                    print(f"⚠️ Response không phải JSON hợp lệ cho ID: {geometry_id}")
                    return response_data  # Vẫn lưu dù không phải JSON

        except urllib.error.URLError as e:
            print(f"❌ URL Error cho ID {geometry_id}: {e}")
            return None
        except Exception as e:
            print(f"❌ Exception cho ID {geometry_id}: {e}")
            return None
    
    def save_geometry_to_db(self, maxa, geometry_data):
        """Lưu geometry data vào database với pti_id = maxa (giá trị gốc)"""
        if not geometry_data:
            return False

        try:
            # Escape JSON data cho SQL
            escaped_data = geometry_data.replace("'", "\\'").replace('"', '\\"')

            insert_sql = f"""INSERT INTO ward_geometry (pti_id, data) VALUES ({maxa}, '{escaped_data}')"""

            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', insert_sql
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return True
            else:
                print(f"❌ Lỗi insert geometry: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Exception save geometry: {e}")
            return False
    
    def crawl_all_geometry(self, skip_existing=True, start_from=None, limit=None, delay_range=(0.2, 1)):
        """Crawl geometry cho tất cả xã phường"""
        print("🚀 BẮT ĐẦU CRAWL GEOMETRY DATA")
        print("=" * 60)

        # Lấy danh sách xã phường
        xaphuong_list = self.get_xaphuong_data()
        if not xaphuong_list:
            print("❌ Không có dữ liệu xã phường để crawl")
            return

        # Filter theo start_from nếu có
        if start_from:
            xaphuong_list = [x for x in xaphuong_list if x['id'] >= start_from]
            print(f"🔄 Bắt đầu từ ID: {start_from}")

        # Limit số lượng nếu có
        if limit:
            xaphuong_list = xaphuong_list[:limit]
            print(f"🔄 Giới hạn: {limit} xã phường")

        print(f"📋 Sẽ crawl geometry cho {len(xaphuong_list)} xã phường")
        print(f"⏱️ Delay giữa requests: {delay_range[0]}-{delay_range[1]} giây")
        print()

        # Thống kê
        total_success = 0
        total_skipped = 0
        total_failed = 0

        for i, xaphuong in enumerate(xaphuong_list, 1):
            maxa = xaphuong['maxa']  # Giá trị gốc để lưu vào DB
            api_id = maxa + 1  # maxa + 1 để gọi API
            tenhc = xaphuong['tenhc']

            print(f"[{i}/{len(xaphuong_list)}] 🏘️ {tenhc} (maxa={maxa}, api_id={api_id})")

            # Kiểm tra đã có dữ liệu chưa (dùng maxa gốc)
            if skip_existing and self.check_existing_geometry(maxa):
                print(f"⏭️ Đã có geometry data, bỏ qua...")
                total_skipped += 1
                print()
                continue

            # Crawl geometry data (dùng api_id = maxa + 1)
            try:
                geometry_data = self.crawl_geometry_data(api_id)

                if geometry_data:
                    # Lưu vào database (dùng maxa gốc)
                    if self.save_geometry_to_db(maxa, geometry_data):
                        total_success += 1
                        print(f"✅ Thành công!")
                    else:
                        total_failed += 1
                        print(f"❌ Lỗi lưu database!")
                else:
                    total_failed += 1
                    print(f"❌ Không lấy được geometry data!")

                # Random delay để tránh bị block
                if i < len(xaphuong_list):
                    delay = random.uniform(delay_range[0], delay_range[1])
                    print(f"⏳ Chờ {delay:.1f} giây...")
                    time.sleep(delay)

            except Exception as e:
                print(f"❌ Exception: {e}")
                total_failed += 1

            print()
        
        # Báo cáo kết quả
        print("=" * 60)
        print("📊 KẾT QUẢ CRAWL GEOMETRY")
        print("=" * 60)
        print(f"✅ Thành công: {total_success}")
        print(f"⏭️ Bỏ qua: {total_skipped}")
        print(f"❌ Thất bại: {total_failed}")
        print(f"📋 Tổng cộng: {len(xaphuong_list)}")
        
        # Kiểm tra tổng số geometry data
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', 
                'SELECT COUNT(*) as total FROM ward_geometry'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    total_records = lines[1]
                    print(f"📊 Tổng số geometry data trong DB: {total_records}")
        except:
            pass

def main():
    """Hàm main"""
    print("🔧 CRAWL GEOMETRY DATA TOOL")
    print("=" * 60)
    
    crawler = GeometryCrawler()
    
    # Tùy chọn crawl
    print("Tùy chọn crawl:")
    print("1. Crawl tất cả (bỏ qua đã có)")
    print("2. Crawl từ ID cụ thể")
    print("3. Crawl giới hạn số lượng")
    print("4. Crawl lại tất cả (ghi đè)")
    print("5. Test với 1 record")
    
    choice = input("Chọn (1-5): ").strip()
    
    if choice == "1":
        crawler.crawl_all_geometry(skip_existing=True)
    elif choice == "2":
        start_from = int(input("Bắt đầu từ ID xã phường: "))
        crawler.crawl_all_geometry(skip_existing=True, start_from=start_from)
    elif choice == "3":
        limit = int(input("Giới hạn số xã phường: "))
        crawler.crawl_all_geometry(skip_existing=True, limit=limit)
    elif choice == "4":
        crawler.crawl_all_geometry(skip_existing=False)
    elif choice == "5":
        crawler.crawl_all_geometry(skip_existing=True, limit=1)
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
