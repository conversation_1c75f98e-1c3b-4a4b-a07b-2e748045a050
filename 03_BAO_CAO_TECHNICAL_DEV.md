# BÁO CÁO KỸ THUẬT CHI TIẾT
## Cập nhật <PERSON>ệ thống Hành chính Việt Nam
**<PERSON><PERSON><PERSON> cho: Development Team & Technical Leads**

---

## 🏗️ KIẾN TRÚC HỆ THỐNG

### Database Schema hiện tại:
```
urbox/
├── tinhthanh (34 records) ✅
├── xaphuong (3,321 records) ✅  
├── ward_geometry (3,312 records) ✅
├── province (legacy - cần update)
├── ward (legacy - cần update)
├── brand_office (cần sync coordinates)
├── brand_store (cần sync coordinates)
├── gift_receiver (cần migration flag)
└── address (cần migration flag)
```

### Data Flow:
```
tinhthanh.json → tinhthanh table
     ↓
API crawl → xaphuong table  
     ↓
Geometry API → ward_geometry table
     ↓
[NEXT] Database migration & API updates
```

---

## ✅ COMPLETED TASKS (5/21)

### Task 1: Crawl Tỉnh Thành ✅
**Technical Details:**
- **Input:** `tinhthanh.json` (34 provinces)
- **Output:** `tinhthanh` table
- **Tool:** `import_tinhthanh.py`
- **Schema:**
```sql
CREATE TABLE tinhthanh (
  id INT AUTO_INCREMENT PRIMARY KEY,
  mahc INT NOT NULL UNIQUE,
  tentinh VARCHAR(255) NOT NULL,
  dientichkm2 VARCHAR(50),
  dansonguoi VARCHAR(50),
  kinhdo DECIMAL(10,6),
  vido DECIMAL(10,6),
  -- ... other fields
);
```

### Task 2: Crawl Xã Phường ✅
**Technical Details:**
- **Source:** API endpoints using `mahc` from tinhthanh
- **Output:** `xaphuong` table (3,321 records)
- **Tools:** `crawl_all_xaphuong.py`, `crawl_xaphuong_api.py`
- **Key Logic:** `pti_id = maxa + 1` for API calls, store `maxa` in DB

### Task 3-4: Crawl Geometry Data ✅
**Technical Details:**
- **Source:** `https://sapnhap.bando.com.vn/pread_json`
- **Output:** `ward_geometry` table (3,312 records)
- **Format:** GeoJSON with MultiPolygon coordinates
- **Size:** ~26MB geometry data
- **Tools:** `crawl_geometry.py`, `crawl_missing_geometry.py`

---

## 📋 PENDING TASKS (17/21)

### PHASE 2: Database Migration (Tasks 5-14)

#### Task 5: Province Table Update 🔄 READY TO START
**Requirement:** Bảng province chứa tỉnh cũ và mới với mapping
```sql
ALTER TABLE province ADD COLUMN (
  new_province_id INT,
  is_merge TINYINT DEFAULT 1 COMMENT '1=old, 2=new'
);

-- Example data structure:
-- Hà Nam (old): is_merge=1, new_province_id=6 (Ninh Bình)
-- Nam Định (old): is_merge=1, new_province_id=6 (Ninh Bình)  
-- Ninh Bình (new): is_merge=2, new_province_id=NULL
```

**Implementation Plan:**
1. Backup existing province table
2. Add new columns
3. Import 34 new provinces with is_merge=2
4. Map existing provinces to new ones
5. Update queries to filter by is_merge=2

#### Task 6: Ward Table Update 🔄 READY TO START
**Requirement:** Mapping xã cũ → xã mới (all merged, none unchanged)
```sql
ALTER TABLE ward ADD COLUMN (
  new_ward_id INT,
  is_merge TINYINT DEFAULT 1 COMMENT '1=old, 2=new'
);
```

**Challenge:** Need business logic to map old wards to new ones

#### Task 7: Brand Office Coordinate Sync ✅ COMPLETED
**Scope:** brand_office table
**Tools Created:**
- `update_brand_office_coordinates.py` - Main geocoding tool
- `test_brand_office_db.py` - Database testing
- `GOOGLE_MAPS_API_SETUP.md` - API setup guide
- `README_BRAND_OFFICE_GEOCODING.md` - Usage documentation

**Features:**
- Google Maps Geocoding API integration
- Rate limiting and error handling
- Dry run mode for testing
- CSV export with detailed results
- Database direct update capability

#### Task 8: Brand Store Coordinate Sync 🔄 READY TO START
**Scope:** brand_store table
**Dependencies:** Task 7 completed (same approach)

#### Tasks 9-10: Address Update using Geometry 🔄 COMPLEX
**Requirement:** Update title field using geometry data
```sql
-- Current: "123 Nguyễn Văn A, Phường B, Quận C, Tỉnh D"
-- Target: "123 Nguyễn Văn A, Xã/Phường B, Tỉnh D" (remove quận/huyện)
```

**Technical Approach:**
1. Parse existing title field
2. Point-in-polygon check using ward_geometry
3. Reconstruct title with new structure
4. Batch update with rollback capability

**Libraries needed:**
- PostGIS for spatial queries, OR
- Python shapely for geometry operations

#### Tasks 11-12: Migration Flags 🔄 SIMPLE
**Tables:** gift_receiver, address
```sql
ALTER TABLE gift_receiver ADD COLUMN use_new_structure BOOLEAN DEFAULT FALSE;
ALTER TABLE address ADD COLUMN use_new_structure BOOLEAN DEFAULT FALSE;
```

#### Tasks 13-14: Cart Analysis 📋 PENDING ANALYSIS
**Status:** Đang phân tích requirements

### PHASE 3: API Development (Tasks 15-21)

#### Core APIs (Tasks 15-16) 📋 DESIGN PHASE
```javascript
// Task 15: GET /api/provinces
{
  "data": [
    {
      "id": 1,
      "mahc": 1,
      "name": "Thủ đô Hà Nội",
      "coordinates": [105.698, 21.0001]
    }
  ]
}

// Task 16: GET /api/provinces/{id}/wards
{
  "data": [
    {
      "id": 1,
      "maxa": 1290,
      "name": "Xã An Khánh",
      "province_id": 1
    }
  ]
}
```

#### Partner APIs (Tasks 17-19) 📋 INTEGRATION DESIGN
- **Task 17:** Whitelabel API updates
- **Task 18:** CPV partner API compatibility
- **Task 19:** NHANH integration API

#### Client APIs (Tasks 20-21) 📋 CLIENT UPDATES
- **Task 20:** Mobile app API endpoints
- **Task 21:** Giftlink API integration

---

## 🛠️ TECHNICAL IMPLEMENTATION PLAN

### Phase 2A: Database Structure (Week 1)
```bash
# Day 1-2: Schema updates
- Backup production database
- Add new columns to province/ward tables
- Import new province/ward data

# Day 3-4: Data migration
- Map old provinces to new ones
- Coordinate sync for brand tables
- Add migration flags

# Day 5: Testing & validation
- Data integrity checks
- Performance testing
- Rollback procedures
```

### Phase 2B: Geometry Integration (Week 2)
```bash
# Day 1-3: Geometry processing
- Setup spatial query capability
- Implement point-in-polygon logic
- Batch process brand_office addresses

# Day 4-5: Address updates
- Update brand_store addresses
- Validate results
- Performance optimization
```

### Phase 3: API Development (Week 3-4)
```bash
# Week 3: Core APIs
- Implement province/ward listing APIs
- Add caching layer
- API documentation

# Week 4: Integration APIs
- Partner API updates
- Client API modifications
- Integration testing
```

---

## 🔧 TECHNICAL REQUIREMENTS

### Development Environment:
- **Database:** MySQL 8.0+ with spatial support
- **Backend:** PHP/Laravel or Node.js/Express
- **Caching:** Redis for API responses
- **Monitoring:** Application performance monitoring

### Libraries & Tools:
```json
{
  "spatial": ["PostGIS", "Shapely", "Turf.js"],
  "geocoding": ["Google Maps API", "OpenStreetMap"],
  "testing": ["PHPUnit", "Jest", "Postman"],
  "monitoring": ["New Relic", "DataDog"]
}
```

### Performance Targets:
- **API Response:** <200ms (95th percentile)
- **Database Queries:** <100ms for address lookups
- **Geometry Queries:** <500ms for point-in-polygon
- **Batch Processing:** 1000 records/minute for address updates

---

## 🚨 TECHNICAL RISKS & MITIGATION

### High Risk:
1. **Geometry Query Performance**
   - Risk: Point-in-polygon queries too slow
   - Mitigation: Spatial indexes, query optimization

2. **Data Migration Complexity**
   - Risk: Old→New mapping logic errors
   - Mitigation: Extensive testing, rollback procedures

3. **API Breaking Changes**
   - Risk: Partner integrations fail
   - Mitigation: API versioning, backward compatibility

### Medium Risk:
1. **Coordinate Accuracy**
   - Risk: Geocoding service limitations
   - Mitigation: Multiple geocoding sources, manual verification

2. **Database Performance**
   - Risk: Large table updates cause locks
   - Mitigation: Batch processing, off-peak deployment

---

## 📊 MONITORING & METRICS

### Development Metrics:
- **Code Coverage:** >80% for new code
- **API Tests:** 100% endpoint coverage
- **Performance Tests:** All APIs under target response time

### Production Metrics:
- **Error Rate:** <0.1% for address-related operations
- **Response Time:** 95th percentile under targets
- **Data Accuracy:** >99.5% address validation success

---

## 🔄 DEPLOYMENT STRATEGY

### Blue-Green Deployment:
1. **Blue (Current):** Existing system with old data
2. **Green (New):** Updated system with new data
3. **Migration:** Gradual traffic shift with rollback capability

### Database Migration:
1. **Schema updates** during maintenance window
2. **Data migration** in background
3. **API deployment** with feature flags
4. **Gradual rollout** by user percentage

---

*Báo cáo kỹ thuật được chuẩn bị bởi: Development Team*  
*Ngày: 11/01/2025*
