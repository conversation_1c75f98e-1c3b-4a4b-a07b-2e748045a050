#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import json
import time
from datetime import datetime
from crawl_xaphuong_api import XaPhuongCrawler

class AllXaPhuongCrawler:
    def __init__(self):
        self.crawler = XaPhuongCrawler()
        
    def get_provinces_from_db(self):
        """Lấy danh sách tỉnh thành từ bảng tinhthanh"""
        print("🔍 Lấy danh sách tỉnh thành từ database...")
        
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', 
                'SELECT mahc, tentinh FROM tinhthanh ORDER BY mahc'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Lỗi query database: {result.stderr}")
                return []
            
            # Parse kết quả
            lines = result.stdout.strip().split('\n')
            provinces = []
            
            for line in lines[1:]:  # Bỏ header
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        mahc = int(parts[0])
                        tentinh = parts[1]
                        provinces.append({'mahc': mahc, 'tentinh': tentinh})
            
            print(f"✅ Tìm thấy {len(provinces)} tỉnh thành")
            return provinces
            
        except Exception as e:
            print(f"❌ Lỗi get provinces: {e}")
            return []
    
    def check_existing_data(self, mahc):
        """Kiểm tra xem đã có dữ liệu cho tỉnh này chưa"""
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', 
                f'SELECT COUNT(*) FROM xaphuong WHERE matinh = {mahc}'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    count = int(lines[1])
                    return count > 0
            return False
            
        except Exception as e:
            print(f"❌ Lỗi check existing data: {e}")
            return False
    
    def crawl_all_provinces(self, skip_existing=True, start_from=None, limit=None):
        """Crawl tất cả các tỉnh thành"""
        print("🚀 BẮT ĐẦU CRAWL TẤT CẢ TỈNH THÀNH")
        print("=" * 60)
        
        # Lấy danh sách tỉnh
        provinces = self.get_provinces_from_db()
        if not provinces:
            print("❌ Không có dữ liệu tỉnh thành để crawl")
            return
        
        # Filter theo start_from nếu có
        if start_from:
            provinces = [p for p in provinces if p['mahc'] >= start_from]
            print(f"🔄 Bắt đầu từ mã HC: {start_from}")
        
        # Limit số lượng nếu có
        if limit:
            provinces = provinces[:limit]
            print(f"🔄 Giới hạn: {limit} tỉnh")
        
        print(f"📋 Sẽ crawl {len(provinces)} tỉnh thành")
        print()
        
        # Thống kê
        total_success = 0
        total_skipped = 0
        total_failed = 0
        
        for i, province in enumerate(provinces, 1):
            mahc = province['mahc']
            tentinh = province['tentinh']
            
            print(f"[{i}/{len(provinces)}] 🏛️ {tentinh} (mahc={mahc})")
            
            # Kiểm tra đã có dữ liệu chưa
            if skip_existing and self.check_existing_data(mahc):
                print(f"⏭️ Đã có dữ liệu, bỏ qua...")
                total_skipped += 1
                print()
                continue
            
            # Crawl dữ liệu
            try:
                success = self.crawler.crawl_and_save(mahc)
                
                if success:
                    total_success += 1
                    print(f"✅ Thành công!")
                else:
                    total_failed += 1
                    print(f"❌ Thất bại!")
                
                # Delay giữa các request
                if i < len(provinces):
                    print("⏳ Chờ 2 giây...")
                    time.sleep(2)
                    
            except Exception as e:
                print(f"❌ Exception: {e}")
                total_failed += 1
            
            print()
        
        # Báo cáo kết quả
        print("=" * 60)
        print("📊 KẾT QUẢ CRAWL")
        print("=" * 60)
        print(f"✅ Thành công: {total_success}")
        print(f"⏭️ Bỏ qua: {total_skipped}")
        print(f"❌ Thất bại: {total_failed}")
        print(f"📋 Tổng cộng: {len(provinces)}")
        
        # Kiểm tra tổng số dữ liệu
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', 
                'SELECT COUNT(*) as total FROM xaphuong'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    total_records = lines[1]
                    print(f"📊 Tổng số xã phường trong DB: {total_records}")
        except:
            pass

def main():
    """Hàm main"""
    print("🔧 CRAWL ALL XÃ PHƯỜNG TOOL")
    print("=" * 60)
    
    crawler = AllXaPhuongCrawler()
    
    # Tùy chọn crawl
    print("Tùy chọn crawl:")
    print("1. Crawl tất cả (bỏ qua đã có)")
    print("2. Crawl từ mã HC cụ thể")
    print("3. Crawl giới hạn số lượng")
    print("4. Crawl lại tất cả (ghi đè)")
    
    choice = input("Chọn (1-4): ").strip()
    
    if choice == "1":
        crawler.crawl_all_provinces(skip_existing=True)
    elif choice == "2":
        start_from = int(input("Bắt đầu từ mã HC: "))
        crawler.crawl_all_provinces(skip_existing=True, start_from=start_from)
    elif choice == "3":
        limit = int(input("Giới hạn số tỉnh: "))
        crawler.crawl_all_provinces(skip_existing=True, limit=limit)
    elif choice == "4":
        crawler.crawl_all_provinces(skip_existing=False)
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
