# Hướng dẫn cấu hình Google Maps API

## 1. Tạo Google Maps API Key

### Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> Google Cloud Console
- Đi tới: https://console.cloud.google.com/
- Đăng nhập bằng tài khoản Google

### Bước 2: Tạo hoặc chọn Project
- Tạo project mới hoặc chọn project hiện có
- Ghi nhớ Project ID

### Bước 3: Kích hoạt Geocoding API
- Vào **APIs & Services** > **Library**
- Tìm kiếm "Geocoding API"
- Click **Enable** để kích hoạt

### Bước 4: Tạo API Key
- Vào **APIs & Services** > **Credentials**
- Click **Create Credentials** > **API Key**
- Copy API Key được tạo

### Bước 5: Bảo mật API Key (Khuyến nghị)
- Click vào API Key vừa tạo
- Trong **API restrictions**, chọn **Restrict key**
- Chọn **Geocoding API**
- Trong **Application restrictions**, có thể chọn:
  - **IP addresses** (nếu chạy từ server cố định)
  - **HTTP referrers** (nếu chạy từ website)
  - **None** (không khuyến nghị cho production)

## 2. Cấu hình Tool

### Cách 1: Sửa trực tiếp trong code
```python
# Trong file update_brand_office_coordinates.py
geocoder = BrandOfficeGeocoder(api_key="YOUR_ACTUAL_API_KEY_HERE")
```

### Cách 2: Sử dụng biến môi trường
```bash
export GOOGLE_MAPS_API_KEY="your_api_key_here"
python3 update_brand_office_coordinates.py
```

### Cách 3: Nhập khi chạy tool
- Chọn option 5 trong menu
- Nhập API key khi được yêu cầu

## 3. Giá cả và Quota

### Pricing (tính đến 2024)
- **Geocoding API**: $5 per 1,000 requests
- **Free tier**: $200 credit mỗi tháng (≈ 40,000 requests)

### Rate Limits
- **Default**: 50 requests per second
- **Daily quota**: Tùy thuộc vào billing account

### Tối ưu hóa chi phí
- Sử dụng **dry run** để test trước
- Batch processing với delay giữa requests
- Cache kết quả để tránh duplicate requests
- Sử dụng **region** và **language** parameters

## 4. Cấu trúc Database

Tool này yêu cầu bảng `brand_office` có cấu trúc:

```sql
CREATE TABLE brand_office (
    id INT PRIMARY KEY,
    name VARCHAR(255),
    address_old TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    -- other fields...
);
```

## 5. Sử dụng Tool

### Test mode (không cập nhật database)
```bash
python3 update_brand_office_coordinates.py
# Chọn option 1 hoặc 2 để test
```

### Production mode
```bash
python3 update_brand_office_coordinates.py
# Chọn option 3, 4 hoặc 5 để cập nhật thực tế
```

### Output
- **Console**: Progress và kết quả real-time
- **CSV file**: Báo cáo chi tiết với timestamp
  - `brand_office_geocoding_YYYYMMDD_HHMMSS.csv`

## 6. Troubleshooting

### Lỗi thường gặp

#### "OVER_QUERY_LIMIT"
- Đã vượt quota hoặc rate limit
- **Giải pháp**: Tăng delay, kiểm tra billing

#### "REQUEST_DENIED"
- API key không hợp lệ hoặc bị restrict
- **Giải pháp**: Kiểm tra API key và restrictions

#### "ZERO_RESULTS"
- Không tìm thấy địa chỉ
- **Giải pháp**: Kiểm tra format địa chỉ, thêm thông tin

#### "INVALID_REQUEST"
- Request không hợp lệ
- **Giải pháp**: Kiểm tra parameters

### Debug tips
- Sử dụng dry run để test
- Kiểm tra API key trong Google Cloud Console
- Xem logs trong Cloud Console > APIs & Services > Metrics

## 7. Best Practices

### Địa chỉ input
- Càng chi tiết càng tốt
- Bao gồm thành phố, quận/huyện
- Sử dụng tiếng Việt có dấu
- Ví dụ: "123 Nguyễn Huệ, Quận 1, TP.HCM"

### Performance
- Batch processing với delay 100-200ms
- Sử dụng concurrent requests (cẩn thận với rate limit)
- Cache results để tránh duplicate calls

### Monitoring
- Theo dõi usage trong Google Cloud Console
- Set up billing alerts
- Monitor error rates

## 8. Alternatives

Nếu không muốn sử dụng Google Maps:

### OpenStreetMap (Nominatim)
- Free
- Rate limit: 1 request/second
- Độ chính xác thấp hơn

### Here API
- Có free tier
- Good cho enterprise

### MapBox
- Có free tier
- Good performance

## 9. Security Notes

- **Không commit API key** vào git
- Sử dụng environment variables
- Restrict API key theo IP/domain
- Monitor usage thường xuyên
- Rotate API key định kỳ
