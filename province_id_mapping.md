# Bảng Mapping ID Tỉnh Thành

## Danh sách ID từ bảng `tinhthanh` (field `mahc`)

| ID | Tên Tỉnh Thành | Ghi chú |
|----|----------------|---------|
| 1  | Thủ đô Hà Nội | Thủ đô |
| 2  | tỉnh Bắc Ninh | |
| 3  | tỉnh Quảng Ninh | |
| 4  | thành phố Hải Phòng | Thành phố trực thuộc TW |
| 5  | tỉnh Hưng Yên | |
| 6  | tỉnh Ninh Bình | |
| 7  | tỉnh Cao Bằng | |
| 8  | tỉnh Tuy<PERSON><PERSON> | |
| 9  | tỉnh Lào <PERSON> | |
| 10 | tỉnh Thái <PERSON> | |
| 11 | tỉnh Lạng Sơn | |
| 12 | tỉnh Phú Thọ | |
| 13 | tỉnh Điện Biên | |
| 14 | tỉnh Lai Châu | |
| 15 | tỉnh Sơn La | |
| 16 | tỉnh Thanh Hóa | |
| 17 | tỉnh Ngh<PERSON> An | |
| 18 | tỉnh <PERSON><PERSON> | |
| 19 | tỉnh Quảng Trị | |
| 20 | thành phố Huế | Thành phố trực thuộc TW |
| 21 | thành phố Đà Nẵng | Thành phố trực thuộc TW |
| 22 | tỉnh Quảng Ngãi | |
| 23 | tỉnh Khánh Hòa | |
| 24 | tỉnh Gia Lai | |
| 25 | tỉnh Đắk Lắk | |
| 26 | tỉnh Lâm Đồng | |
| 27 | tỉnh Tây Ninh | |
| 28 | tỉnh Đồng Nai | |
| 29 | thành phố Hồ Chí Minh | Thành phố trực thuộc TW |
| 30 | tỉnh Vĩnh Long | |
| 31 | tỉnh Đồng Tháp | |
| 32 | tỉnh An Giang | |
| 33 | thành phố Cần Thơ | Thành phố trực thuộc TW |
| 34 | tỉnh Cà Mau | |

## Các tỉnh thành quan trọng

### Thành phố lớn
- **Hà Nội**: ID = 1
- **TP. Hồ Chí Minh**: ID = 29
- **Đà Nẵng**: ID = 21
- **Hải Phòng**: ID = 4
- **Cần Thơ**: ID = 33

### Miền Bắc
- Hà Nội: 1
- Bắc Ninh: 2
- Quảng Ninh: 3
- Hải Phòng: 4
- Hưng Yên: 5
- Ninh Bình: 6
- Cao Bằng: 7
- Tuyên Quang: 8
- Lào Cai: 9
- Thái Nguyên: 10
- Lạng Sơn: 11
- Phú Thọ: 12
- Điện Biên: 13
- Lai Châu: 14
- Sơn La: 15

### Miền Trung
- Thanh Hóa: 16
- Nghệ An: 17
- Hà Tĩnh: 18
- Quảng Trị: 19
- Huế: 20
- Đà Nẵng: 21
- Quảng Ngãi: 22
- Khánh Hòa: 23
- Gia Lai: 24
- Đắk Lắk: 25
- Lâm Đồng: 26

### Miền Nam
- Tây Ninh: 27
- Đồng Nai: 28
- TP. Hồ Chí Minh: 29
- Vĩnh Long: 30
- Đồng Tháp: 31
- An Giang: 32
- Cần Thơ: 33
- Cà Mau: 34

## Lệnh crawl thường dùng

```bash
# Crawl các thành phố lớn
python3 crawl_all_provinces.py multiple 1,29,21,4,33

# Crawl miền Bắc
python3 crawl_all_provinces.py multiple 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15

# Crawl miền Trung
python3 crawl_all_provinces.py multiple 16,17,18,19,20,21,22,23,24,25,26

# Crawl miền Nam
python3 crawl_all_provinces.py multiple 27,28,29,30,31,32,33,34

# Crawl tất cả
python3 crawl_all_provinces.py all
```

## Kiểm tra dữ liệu

```sql
-- Xem tổng số xã phường theo tỉnh
SELECT
    t.mahc,
    t.tentinh,
    COUNT(x.id) as ward_count
FROM xaphuong x
JOIN tinhthanh t ON x.matinh = t.mahc
GROUP BY t.mahc, t.tentinh
ORDER BY t.mahc;

-- Xem chi tiết xã phường của một tỉnh
SELECT id, matinh, ma, tentinh, loai, tenhc
FROM xaphuong
WHERE matinh = 1
ORDER BY tenhc
LIMIT 10;
```
