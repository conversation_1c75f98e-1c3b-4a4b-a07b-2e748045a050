#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv

def process_ward_csv():
    """
    Xử lý file ward.csv:
    1. Đọc province.csv để tạo mapping province_pti_id -> id
    2. <PERSON><PERSON><PERSON> nhật province_id trong ward.csv dựa trên mapping
    3. Lưu kết quả thành ward_new.csv
    """
    
    print("🔄 Bắt đầu xử lý dữ liệu CSV...")
    
    # 1. Đọc province.csv và tạo mapping
    print("📖 Đọc file province.csv...")
    province_mapping = {}

    try:
        with open('province.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                pti_id = row['province_pti_id'].strip()
                db_id = row['id'].strip()
                if pti_id and db_id:
                    # Tạo mapping cho cả format số và format có leading zero
                    province_mapping[pti_id] = db_id
                    province_mapping[pti_id.zfill(2)] = db_id  # Format 01, 02, 03...
        
        print(f"✅ Đã tạo mapping cho {len(province_mapping)} tỉnh/thành phố")
        print("📋 Mapping preview:")
        for i, (pti_id, db_id) in enumerate(list(province_mapping.items())[:5]):
            print(f"   PTI ID {pti_id} -> DB ID {db_id}")
        if len(province_mapping) > 5:
            print(f"   ... và {len(province_mapping) - 5} mapping khác")
            
    except Exception as e:
        print(f"❌ Lỗi khi đọc province.csv: {e}")
        return
    
    # 2. Xử lý ward.csv
    print("\n📖 Đọc và xử lý file ward.csv...")
    
    updated_rows = []
    matched_count = 0
    total_count = 0
    
    try:
        with open('ward.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            fieldnames = reader.fieldnames
            
            for row in reader:
                total_count += 1
                province_pti_id = row['province_pti_id'].strip()
                
                # Tìm ID tương ứng từ mapping
                if province_pti_id in province_mapping:
                    row['province_id'] = province_mapping[province_pti_id]
                    matched_count += 1
                else:
                    # Giữ nguyên giá trị cũ (có thể là rỗng)
                    pass
                
                updated_rows.append(row)
        
        print(f"✅ Đã xử lý {total_count} records")
        print(f"🎯 Matched và cập nhật: {matched_count} records")
        print(f"❓ Không match: {total_count - matched_count} records")
        
    except Exception as e:
        print(f"❌ Lỗi khi đọc ward.csv: {e}")
        return
    
    # 3. Lưu kết quả thành ward_new.csv
    print("\n💾 Lưu kết quả thành ward_new.csv...")
    
    try:
        with open('ward_new.csv', 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(updated_rows)
        
        print("✅ Đã lưu thành công file ward_new.csv")
        
        # Hiển thị một vài dòng mẫu
        print("\n📋 Preview ward_new.csv (5 dòng đầu):")
        with open('ward_new.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                if i >= 5:
                    break
                print(f"   {row['ward_title']} - Province PTI: {row['province_pti_id']} - Province ID: {row['province_id']}")
                
    except Exception as e:
        print(f"❌ Lỗi khi lưu ward_new.csv: {e}")
        return
    
    print(f"\n🎉 Hoàn thành! Đã cập nhật {matched_count}/{total_count} records thành công.")

if __name__ == "__main__":
    process_ward_csv()
