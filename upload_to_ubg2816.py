#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Upload geometry crawl files to UBG-2816
"""

import os
import requests

# Configuration
JIRA_CONFIG = {
    'base_url': 'https://urbox.atlassian.net',
    'email': '<EMAIL>',
    'api_token': 'ATATT3xFfGF0W9N5Xovi_R3lZ1PZKOU21ZqCjIjle74xJui2tEeXpIFz-6q7cPRAC9I52rCww26E2dwmNlvPINe_fBwsqbMkoGhgMsJbLwxFpbOdaHBb-Lptk5M1BydFujufdv7T7fqi2SsWZicnizGMbQibBaRkac7nfW2C4nqTiqWLS6rrVY0=548B34C7'
}

ISSUE_KEY = "UBG-2816"

# Files to upload
geometry_files = [
    "crawl_geometry.py",
    "crawl_single_geometry.py", 
    "crawl_missing_geometry.py",
    "create_ward_geometry_table.sql",
    "sample_geometry.json"
]

def upload_file_to_jira(issue_key, file_path):
    """Upload a single file to Jira issue"""
    if not os.path.exists(file_path):
        return False, f"File not found: {file_path}"
    
    url = f"{JIRA_CONFIG['base_url']}/rest/api/3/issue/{issue_key}/attachments"
    
    file_name = os.path.basename(file_path)
    file_size = os.path.getsize(file_path)
    
    print(f"📎 Uploading: {file_name} ({file_size:,} bytes)")
    
    try:
        headers = {'X-Atlassian-Token': 'no-check'}
        
        with open(file_path, 'rb') as file:
            files = {'file': (file_name, file, 'application/octet-stream')}
            
            response = requests.post(
                url, 
                files=files, 
                headers=headers,
                auth=(JIRA_CONFIG['email'], JIRA_CONFIG['api_token'])
            )
            
            if response.status_code == 200:
                attachment_data = response.json()[0]
                print(f"  ✅ Success - ID: {attachment_data['id']}")
                return True, attachment_data
            else:
                print(f"  ❌ Failed: {response.status_code}")
                print(f"  Response: {response.text}")
                return False, f"HTTP {response.status_code}"
                
    except Exception as e:
        print(f"  ❌ Exception: {e}")
        return False, str(e)

def main():
    """Main upload function"""
    print("🚀 UPLOADING GEOMETRY CRAWL FILES TO UBG-2816")
    print("=" * 60)
    
    # Check which files exist
    existing_files = []
    missing_files = []
    
    for file_path in geometry_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            file_size = os.path.getsize(file_path)
            print(f"✅ Found: {file_path} ({file_size:,} bytes)")
        else:
            missing_files.append(file_path)
            print(f"❌ Missing: {file_path}")
    
    print(f"\n📊 Summary: {len(existing_files)} found, {len(missing_files)} missing")
    
    if not existing_files:
        print("❌ No files to upload!")
        return
    
    if missing_files:
        print(f"⚠️ Missing files: {', '.join(missing_files)}")
    
    print(f"\n🎯 Target issue: {ISSUE_KEY}")
    print(f"📋 Files to upload: {len(existing_files)}")
    print()
    
    # Upload files
    successful = 0
    failed = 0
    
    for file_path in existing_files:
        success, result = upload_file_to_jira(ISSUE_KEY, file_path)
        if success:
            successful += 1
        else:
            failed += 1
        print()
    
    # Summary
    print("=" * 60)
    print("📊 UPLOAD SUMMARY")
    print("=" * 60)
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📋 Total: {len(existing_files)}")
    
    if successful > 0:
        print(f"\n🔗 View issue: {JIRA_CONFIG['base_url']}/browse/{ISSUE_KEY}")
        print("🎉 Upload completed!")
    else:
        print("\n❌ All uploads failed!")

if __name__ == "__main__":
    main()
