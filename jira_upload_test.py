#!/usr/bin/env python3
"""
Jira File Upload Test Tool
Sử dụng Jira REST API để test upload file với OAuth authentication
"""

import requests
import os
import json
import sys
from pathlib import Path

class JiraUploadTester:
    def __init__(self, jira_url, oauth_token, cloud_id=None):
        """
        Initialize Jira Upload Tester
        
        Args:
            jira_url: URL của Jira instance (ví dụ: https://your-company.atlassian.net)
            oauth_token: OAuth access token
            cloud_id: Atlassian Cloud ID (optional)
        """
        self.jira_url = jira_url.rstrip('/')
        self.oauth_token = oauth_token
        self.cloud_id = cloud_id
        
        # Setup headers
        self.headers = {
            'Authorization': f'Bearer {oauth_token}',
            'Accept': 'application/json'
        }
        
        if cloud_id:
            self.headers['X-Atlassian-Cloud-Id'] = cloud_id
    
    def upload_attachment(self, issue_key, file_path):
        """
        Upload file attachment to Jira issue
        
        Args:
            issue_key: <PERSON>ra issue key (ví dụ: UBG-2792)
            file_path: Đường dẫn đến file cần upload
            
        Returns:
            dict: Response từ Jira API
        """
        if not os.path.exists(file_path):
            return {"error": f"File không tồn tại: {file_path}"}
        
        # Endpoint để upload attachment
        url = f"{self.jira_url}/rest/api/3/issue/{issue_key}/attachments"
        
        # Prepare file for upload
        file_name = os.path.basename(file_path)
        
        try:
            with open(file_path, 'rb') as file:
                files = {
                    'file': (file_name, file, 'application/octet-stream')
                }
                
                # Headers cho multipart upload (không include Content-Type)
                upload_headers = self.headers.copy()
                # Xóa Content-Type để requests tự động set với boundary
                upload_headers.pop('Content-Type', None)
                
                print(f"🔄 Uploading {file_name} to {issue_key}...")
                print(f"📍 URL: {url}")
                
                response = requests.post(
                    url,
                    headers=upload_headers,
                    files=files,
                    timeout=30
                )
                
                print(f"📊 Status Code: {response.status_code}")
                print(f"📋 Response Headers: {dict(response.headers)}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Upload thành công!")
                    print(f"📎 Attachment ID: {result[0]['id']}")
                    print(f"📄 Filename: {result[0]['filename']}")
                    print(f"📏 Size: {result[0]['size']} bytes")
                    return {"success": True, "data": result}
                else:
                    error_text = response.text
                    print(f"❌ Upload thất bại: {response.status_code}")
                    print(f"📝 Error: {error_text}")
                    return {"error": f"HTTP {response.status_code}: {error_text}"}
                    
        except Exception as e:
            print(f"💥 Exception: {str(e)}")
            return {"error": f"Exception: {str(e)}"}
    
    def test_permissions(self):
        """Test quyền truy cập Jira"""
        url = f"{self.jira_url}/rest/api/3/myself"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                user_info = response.json()
                print(f"✅ Authentication thành công!")
                print(f"👤 User: {user_info.get('displayName', 'Unknown')}")
                print(f"📧 Email: {user_info.get('emailAddress', 'Unknown')}")
                return True
            else:
                print(f"❌ Authentication thất bại: {response.status_code}")
                print(f"📝 Error: {response.text}")
                return False
        except Exception as e:
            print(f"💥 Exception khi test permissions: {str(e)}")
            return False
    
    def get_issue_info(self, issue_key):
        """Lấy thông tin issue"""
        url = f"{self.jira_url}/rest/api/3/issue/{issue_key}"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                issue = response.json()
                print(f"📋 Issue: {issue['key']}")
                print(f"📝 Summary: {issue['fields']['summary']}")
                print(f"📎 Current attachments: {len(issue['fields'].get('attachment', []))}")
                return issue
            else:
                print(f"❌ Không thể lấy thông tin issue: {response.status_code}")
                return None
        except Exception as e:
            print(f"💥 Exception: {str(e)}")
            return None

def main():
    """Main function để test upload"""
    print("🚀 Jira File Upload Test Tool")
    print("=" * 50)
    
    # Cấu hình - BẠN CẦN ĐIỀN THÔNG TIN NÀY
    JIRA_URL = "https://urbox.atlassian.net"  # Thay bằng URL Jira của bạn
    OAUTH_TOKEN = ""  # Điền OAuth token của bạn
    CLOUD_ID = ""     # Điền Cloud ID (nếu có)
    
    if not OAUTH_TOKEN:
        print("❌ Vui lòng điền OAUTH_TOKEN trong script!")
        print("💡 Lấy token từ: https://id.atlassian.com/manage-profile/security/api-tokens")
        return
    
    # Khởi tạo tester
    tester = JiraUploadTester(JIRA_URL, OAUTH_TOKEN, CLOUD_ID)
    
    # Test 1: Kiểm tra permissions
    print("\n🔐 Test 1: Kiểm tra quyền truy cập...")
    if not tester.test_permissions():
        print("❌ Không thể kết nối Jira. Kiểm tra lại token!")
        return
    
    # Test 2: Lấy thông tin issue
    issue_key = "UBG-2792"
    print(f"\n📋 Test 2: Lấy thông tin issue {issue_key}...")
    issue_info = tester.get_issue_info(issue_key)
    if not issue_info:
        print("❌ Không thể lấy thông tin issue!")
        return
    
    # Test 3: Upload file
    test_file = "test_upload.txt"
    print(f"\n📎 Test 3: Upload file {test_file}...")
    
    if not os.path.exists(test_file):
        print(f"📝 Tạo file test: {test_file}")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("Test file upload to Jira\n")
            f.write(f"Created: {os.popen('date').read().strip()}\n")
            f.write("Purpose: Testing attachment upload with OAuth\n")
    
    result = tester.upload_attachment(issue_key, test_file)
    
    if result.get("success"):
        print("\n🎉 Upload test THÀNH CÔNG!")
        print("✅ Tool có thể upload file lên Jira!")
    else:
        print(f"\n💥 Upload test THẤT BẠI!")
        print(f"❌ Lỗi: {result.get('error', 'Unknown error')}")
    
    print("\n" + "=" * 50)
    print("🏁 Test hoàn thành!")

if __name__ == "__main__":
    main()
