#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tool lấy lat/long từ Google Maps API cho bảng brand_office (Việt Nam)
- Input: address_old
- Output: file SQL update latitude/longitude, file log lỗi CSV
- Giới hạn: 1000 request/lần
- <PERSON><PERSON> trống API key, user tự điền
"""
import csv
import requests
import time
import sys
import os
import subprocess

# --- CONFIG ---
API_KEY = ""  # <-- Nhập Google Maps API key ở đây
MAX_REQUESTS = 1000
DB_HOST = "localhost"
DB_PORT = 3306
DB_USER = "root"
DB_PASS = "root"
DB_NAME = "urbox"

# --- SQL ---
SELECT_SQL = "SELECT id, address_old FROM brand_office"

# --- OUTPUT ---
SQL_UPDATE_FILE = "update_brand_office_latlong.sql"
ERROR_LOG_FILE = "brand_office_latlong_error.csv"

# --- GOOGLE MAPS API ---
GEOCODE_URL = "https://maps.googleapis.com/maps/api/geocode/json"


def get_db_records():
    """Lấy tất cả bản ghi brand_office (id, address_old)"""
    cmd = [
        "mysql", "-u", DB_USER, f"-p{DB_PASS}", "-h", DB_HOST, "-P", str(DB_PORT),
        "-D", DB_NAME, "--batch", "--raw", "-e", SELECT_SQL
    ]
    result = subprocess.run(cmd, capture_output=True, text=True, encoding="utf-8")
    if result.returncode != 0:
        print(f"❌ Lỗi MySQL: {result.stderr}")
        sys.exit(1)
    lines = result.stdout.strip().split('\n')[1:]  # Bỏ header
    records = []
    for line in lines:
        parts = line.split('\t')
        if len(parts) >= 2:
            record_id = int(parts[0])
            address = parts[1].strip()
            if address:
                records.append((record_id, address))
    return records

def geocode_address(address, api_key):
    """Gọi Google Maps API lấy lat/long từ địa chỉ"""
    params = {
        "address": address,
        "region": "vn",
        "key": api_key
    }
    try:
        resp = requests.get(GEOCODE_URL, params=params, timeout=10)
        data = resp.json()
        if data.get("status") == "OK" and data["results"]:
            loc = data["results"][0]["geometry"]["location"]
            return loc["lat"], loc["lng"]
        else:
            return None, None
    except Exception as e:
        return None, None

def main():
    print("🔎 Đang lấy dữ liệu brand_office...")
    records = get_db_records()
    print(f"Tổng số bản ghi: {len(records)}")
    print(f"Giới hạn request: {MAX_REQUESTS}")
    
    api_key = API_KEY
    if not api_key:
        print("⚠️  Chưa có API key. Vui lòng điền vào biến API_KEY trong script!")
        return

    update_sqls = []
    error_rows = []
    count = 0
    for record_id, address in records:
        if count >= MAX_REQUESTS:
            print(f"Đã đạt giới hạn {MAX_REQUESTS} request!")
            break
        print(f"[{count+1}] ID {record_id}: {address}")
        lat, lng = geocode_address(address, api_key)
        if lat is not None and lng is not None:
            update_sql = f"UPDATE brand_office SET latitude = {lat:.6f}, longitude = {lng:.6f} WHERE id = {record_id};"
            update_sqls.append(update_sql)
            print(f"   ✅ {lat:.6f}, {lng:.6f}")
        else:
            error_rows.append({"id": record_id, "address": address})
            print(f"   ❌ Không lấy được tọa độ!")
        count += 1
        time.sleep(0.2)  # Tránh spam API

    # Ghi file SQL update
    with open(SQL_UPDATE_FILE, "w", encoding="utf-8") as f:
        f.write("-- Update latitude/longitude for brand_office\n\n")
        for sql in update_sqls:
            f.write(sql + "\n")
    print(f"💾 Đã ghi {len(update_sqls)} câu lệnh UPDATE vào {SQL_UPDATE_FILE}")

    # Ghi file lỗi
    if error_rows:
        with open(ERROR_LOG_FILE, "w", encoding="utf-8", newline='') as f:
            writer = csv.DictWriter(f, fieldnames=["id", "address"])
            writer.writeheader()
            for row in error_rows:
                writer.writerow(row)
        print(f"⚠️  Có {len(error_rows)} địa chỉ lỗi, đã log vào {ERROR_LOG_FILE}")
    else:
        print("✅ Không có địa chỉ lỗi!")

    print("\nHoàn thành. Bạn có thể chạy file SQL này để cập nhật lat/long cho brand_office.")
    print("Nếu muốn tự động chạy file SQL, hãy dùng script run_sql_update.py.")

if __name__ == "__main__":
    main() 