#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Jira Integration Module for Augment Agent
<PERSON><PERSON><PERSON> hợp tự động tạo task và upload file lên <PERSON>ra
"""

import os
import glob
from jira_auto_uploader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>loader, TASK_TEMPLATES, quick_upload

class JiraIntegration:
    def __init__(self):
        """Initialize Jira integration"""
        self.uploader = JiraAutoUploader()
        
        # File patterns for different types of work
        self.file_patterns = {
            'geometry_crawl': [
                'crawl_geometry.py',
                'crawl_single_geometry.py', 
                'crawl_missing_geometry.py',
                'create_ward_geometry_table.sql',
                'sample_geometry.json'
            ],
            'brand_office_coordinates': [
                'update_brand_office_coordinates.py',
                'test_brand_office_db.py',
                'GOOGLE_MAPS_API_SETUP.md',
                'README_BRAND_OFFICE_GEOCODING.md'
            ],
            'brand_store_coordinates': [
                'update_brand_store_coordinates.py',
                'test_brand_store_db.py',
                'README_BRAND_STORE_GEOCODING.md'
            ],
            'crawl_tools': [
                'crawl_all_xaphuong.py',
                'import_tinhthanh.py',
                'crawl_xaphuong_api.py'
            ],
            'duplicate_analysis': [
                'check_duplicate_maxa.py',
                'check_duplicate_xaphuong.py',
                'duplicate_*.csv'
            ],
            'jira_tools': [
                'jira_file_uploader.py',
                'jira_auto_uploader.py',
                'upload_geometry_files.py',
                'JIRA_API_TOKEN_SETUP.md',
                'README_JIRA_FILE_UPLOADER.md'
            ]
        }
    
    def auto_detect_files(self, work_type):
        """
        Tự động detect files dựa trên loại công việc
        
        Args:
            work_type (str): Loại công việc
            
        Returns:
            list: Danh sách files tồn tại
        """
        if work_type not in self.file_patterns:
            return []
        
        existing_files = []
        patterns = self.file_patterns[work_type]
        
        for pattern in patterns:
            if '*' in pattern:
                # Wildcard pattern
                matches = glob.glob(pattern)
                existing_files.extend(matches)
            else:
                # Exact file name
                if os.path.exists(pattern):
                    existing_files.append(pattern)
        
        return existing_files
    
    def upload_geometry_crawl_files(self, custom_summary=None):
        """Upload geometry crawl files to Jira"""
        files = self.auto_detect_files('geometry_crawl')
        
        custom_data = {}
        if custom_summary:
            custom_data['summary'] = custom_summary
        
        print(f"🎯 Uploading geometry crawl files...")
        print(f"📁 Files found: {len(files)}")
        for f in files:
            print(f"  - {f}")
        
        if not files:
            print("❌ No geometry crawl files found!")
            return None
        
        return quick_upload('geometry_crawl', files, custom_data)
    
    def upload_brand_office_files(self, custom_summary=None):
        """Upload brand office coordinate files to Jira"""
        files = self.auto_detect_files('brand_office_coordinates')
        
        custom_data = {}
        if custom_summary:
            custom_data['summary'] = custom_summary
        
        print(f"🎯 Uploading brand office coordinate files...")
        print(f"📁 Files found: {len(files)}")
        for f in files:
            print(f"  - {f}")
        
        if not files:
            print("❌ No brand office files found!")
            return None
        
        return quick_upload('brand_office_coordinates', files, custom_data)
    
    def upload_jira_tools(self):
        """Upload Jira tools to a new task"""
        files = self.auto_detect_files('jira_tools')
        
        # Custom task data for Jira tools
        task_data = {
            'summary': 'Tạo tool upload file lên Jira',
            'description': 'Đã tạo bộ tool hoàn chỉnh để upload file lên Jira issues một cách tự động.',
            'parent_key': 'UBG-2790',
            'labels': ['UrGift', 'jira', 'upload', 'automation'],
            'tools': [
                'jira_file_uploader.py - Tool chính upload file với interactive menu',
                'jira_auto_uploader.py - Tool tự động tạo task và upload',
                'upload_geometry_files.py - Script chuyên biệt cho geometry files',
                'JIRA_API_TOKEN_SETUP.md - Hướng dẫn tạo API token',
                'README_JIRA_FILE_UPLOADER.md - Hướng dẫn sử dụng chi tiết'
            ],
            'technical_details': [
                'Jira REST API v3 integration',
                'Batch file upload với progress tracking',
                'Pre-configured templates cho các loại task',
                'Auto task creation và transition',
                'Error handling và retry mechanism'
            ],
            'auto_transition': 'Done'
        }
        
        print(f"🎯 Uploading Jira tools...")
        print(f"📁 Files found: {len(files)}")
        for f in files:
            print(f"  - {f}")
        
        if not files:
            print("❌ No Jira tool files found!")
            return None
        
        result = self.uploader.create_task_and_upload(task_data, files)
        
        # Summary
        if result['task_created']:
            print(f"\n🎉 SUCCESS!")
            print(f"📋 Task: {result['task_key']}")
            print(f"🔗 URL: https://urbox.atlassian.net/browse/{result['task_key']}")
            
            if result['files_uploaded']:
                successful = sum(1 for f in result['files_uploaded'] if f['success'])
                print(f"📎 Files uploaded: {successful}/{len(result['files_uploaded'])}")
        
        return result
    
    def upload_custom_files(self, task_summary, files, description=None, parent_key="UBG-2790"):
        """
        Upload custom files với task summary tự định
        
        Args:
            task_summary (str): Tên task
            files (list): Danh sách file paths
            description (str): Mô tả task
            parent_key (str): Parent epic key
        """
        # Filter existing files
        existing_files = [f for f in files if os.path.exists(f)]
        missing_files = [f for f in files if not os.path.exists(f)]
        
        if missing_files:
            print(f"⚠️ Missing files: {', '.join(missing_files)}")
        
        if not existing_files:
            print("❌ No files to upload!")
            return None
        
        # Build task data
        task_data = {
            'summary': task_summary,
            'description': description or f"Upload files cho task: {task_summary}",
            'parent_key': parent_key,
            'labels': ['UrGift', 'file_upload'],
            'auto_transition': 'Done'
        }
        
        print(f"🎯 Creating custom task: {task_summary}")
        print(f"📁 Files to upload: {len(existing_files)}")
        for f in existing_files:
            print(f"  - {f}")
        
        result = self.uploader.create_task_and_upload(task_data, existing_files)
        
        # Summary
        if result['task_created']:
            print(f"\n🎉 SUCCESS!")
            print(f"📋 Task: {result['task_key']}")
            print(f"🔗 URL: https://urbox.atlassian.net/browse/{result['task_key']}")
            
            if result['files_uploaded']:
                successful = sum(1 for f in result['files_uploaded'] if f['success'])
                print(f"📎 Files uploaded: {successful}/{len(result['files_uploaded'])}")
        
        return result

# Global instance for easy access
jira_integration = JiraIntegration()

# Convenience functions
def upload_geometry_files():
    """Quick function to upload geometry crawl files"""
    return jira_integration.upload_geometry_crawl_files()

def upload_brand_office_files():
    """Quick function to upload brand office files"""
    return jira_integration.upload_brand_office_files()

def upload_jira_tools():
    """Quick function to upload Jira tools"""
    return jira_integration.upload_jira_tools()

def upload_files(task_summary, files, description=None):
    """Quick function to upload custom files"""
    return jira_integration.upload_custom_files(task_summary, files, description)

def main():
    """Demo usage"""
    print("🔧 JIRA INTEGRATION DEMO")
    print("=" * 60)
    
    print("Available functions:")
    print("1. upload_geometry_files() - Upload geometry crawl files")
    print("2. upload_brand_office_files() - Upload brand office files") 
    print("3. upload_jira_tools() - Upload Jira tools")
    print("4. upload_files(summary, files) - Upload custom files")
    
    # Example usage:
    # result = upload_geometry_files()
    # result = upload_brand_office_files()
    # result = upload_jira_tools()
    # result = upload_files("Custom Task", ["file1.py", "file2.sql"])

if __name__ == "__main__":
    main()
