#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Jira Integration Module for Augment Agent
T<PERSON><PERSON> hợp tự động tạo task và upload file lên Jira
"""

import os
import glob
import requests
import json
from datetime import datetime

# Try to import config
try:
    from jira_config import get_jira_config
    JIRA_CONFIG = get_jira_config()
except ImportError:
    print("⚠️ jira_config.py not found, using template values")
    JIRA_CONFIG = {
        'base_url': 'https://urbox.atlassian.net',
        'email': '<EMAIL>',
        'api_token': 'ATATT3xFfGF0PkOxIdwgLF6CXs1ZFgxVynm1dkcatj1okvM1OdUYru5wosvc72khb_XW8L1qJgVnXfNZr4N1EiCidbXyMCqHQzN1pWqwL8u4KgYJ5RvzZKWLJJJryJb8R2Iu0Uwt22U7AXkqF_ZQSsy6XoS1AfNalAZMf_BBSHb4USqZdfYwWbk=C2ADFF33',
        'default_assignee': '70121:c41ffafb-2bc9-4512-86bf-f60f459f90b9',
        'default_parent': 'UBG-2790'
    }

class JiraAutoUploader:
    def __init__(self):
        """Auto Jira uploader với config được fill sẵn"""
        # Use configuration from config file
        self.base_url = JIRA_CONFIG['base_url']
        self.email = JIRA_CONFIG['email']
        self.api_token = JIRA_CONFIG['api_token']
        self.default_assignee = JIRA_CONFIG['default_assignee']

        # Session setup
        self.session = requests.Session()
        self.session.auth = (self.email, self.api_token)
        self.session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Atlassian-Token': 'no-check'
        })

    def create_task_and_upload(self, task_data, files_to_upload=None):
        """Tạo task và upload files trong một lần gọi"""
        result = {
            'task_created': False,
            'task_key': None,
            'files_uploaded': [],
            'errors': []
        }

        try:
            # 1. Tạo task trước
            print(f"🎯 Creating task: {task_data['summary']}")
            task_result = self.create_task(task_data)

            if not task_result:
                result['errors'].append("Failed to create task")
                return result

            result['task_created'] = True
            result['task_key'] = task_result['key']
            print(f"✅ Task created: {task_result['key']}")

            # 2. Upload files nếu có
            if files_to_upload:
                print(f"📁 Uploading {len(files_to_upload)} files...")
                upload_results = self.upload_files(task_result['key'], files_to_upload)
                result['files_uploaded'] = upload_results

            # 3. Chuyển trạng thái nếu cần
            if task_data.get('auto_transition'):
                self.transition_task(task_result['key'], task_data['auto_transition'])

            return result

        except Exception as e:
            result['errors'].append(f"Exception: {e}")
            return result

    def create_task(self, task_data):
        """Tạo Jira task"""
        url = f"{self.base_url}/rest/api/3/issue"

        # Build description content
        description_content = []

        # Main description
        if task_data.get('description'):
            description_content.append({
                "type": "paragraph",
                "content": [{"type": "text", "text": task_data['description']}]
            })

        # Tools/files section
        if task_data.get('tools'):
            description_content.append({
                "type": "paragraph",
                "content": [{"type": "text", "text": "Tools đã tạo:", "marks": [{"type": "strong"}]}]
            })

            tool_items = []
            for tool in task_data['tools']:
                tool_items.append({
                    "type": "listItem",
                    "content": [{"type": "paragraph", "content": [{"type": "text", "text": tool}]}]
                })

            description_content.append({
                "type": "bulletList",
                "content": tool_items
            })

        # Technical details
        if task_data.get('technical_details'):
            description_content.append({
                "type": "paragraph",
                "content": [{"type": "text", "text": "Chi tiết kỹ thuật:", "marks": [{"type": "strong"}]}]
            })

            for detail in task_data['technical_details']:
                description_content.append({
                    "type": "paragraph",
                    "content": [{"type": "text", "text": f"• {detail}"}]
                })

        # Attribution
        description_content.extend([
            {"type": "rule"},
            {
                "type": "paragraph",
                "content": [
                    {"type": "text", "text": "Co-authored by"},
                    {
                        "type": "text",
                        "text": " Augment Code",
                        "marks": [{
                            "type": "link",
                            "attrs": {"href": "https://www.augmentcode.com/?utm_source=atlassian&utm_medium=jira_issue&utm_campaign=jira"}
                        }]
                    }
                ]
            }
        ])

        # Build payload
        payload = {
            "fields": {
                "project": {"key": "UBG"},
                "summary": task_data['summary'],
                "description": {
                    "type": "doc",
                    "version": 1,
                    "content": description_content
                },
                "issuetype": {"name": task_data.get('issue_type', 'Task')},
                "assignee": {"accountId": self.default_assignee},
                "priority": {"name": task_data.get('priority', 'Medium')},
                "labels": task_data.get('labels', [])
            }
        }

        # Add parent if specified
        if task_data.get('parent_key'):
            payload['fields']['parent'] = {"key": task_data['parent_key']}

        try:
            response = self.session.post(url, json=payload)

            if response.status_code == 201:
                return response.json()
            else:
                print(f"❌ Failed to create task: {response.status_code}")
                print(f"Response: {response.text}")
                return None

        except Exception as e:
            print(f"❌ Exception creating task: {e}")
            return None

    def upload_files(self, issue_key, file_paths):
        """Upload multiple files to issue"""
        results = []

        for file_path in file_paths:
            if not os.path.exists(file_path):
                results.append({
                    'file': file_path,
                    'success': False,
                    'error': 'File not found'
                })
                continue

            result = self.upload_single_file(issue_key, file_path)
            results.append(result)

        return results

    def upload_single_file(self, issue_key, file_path):
        """Upload single file"""
        url = f"{self.base_url}/rest/api/3/issue/{issue_key}/attachments"

        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)

        print(f"  📎 Uploading: {file_name} ({file_size:,} bytes)")

        try:
            # Remove Content-Type for file upload
            headers = {'X-Atlassian-Token': 'no-check'}

            with open(file_path, 'rb') as file:
                files = {'file': (file_name, file, 'application/octet-stream')}

                response = requests.post(
                    url,
                    files=files,
                    headers=headers,
                    auth=(self.email, self.api_token)
                )

                if response.status_code == 200:
                    attachment_data = response.json()[0]
                    print(f"    ✅ Success - ID: {attachment_data['id']}")
                    return {
                        'file': file_path,
                        'success': True,
                        'attachment_id': attachment_data['id'],
                        'download_url': attachment_data['content']
                    }
                else:
                    print(f"    ❌ Failed: {response.status_code}")
                    return {
                        'file': file_path,
                        'success': False,
                        'error': f"HTTP {response.status_code}: {response.text}"
                    }

        except Exception as e:
            print(f"    ❌ Exception: {e}")
            return {
                'file': file_path,
                'success': False,
                'error': str(e)
            }

    def transition_task(self, issue_key, target_status):
        """Chuyển trạng thái task"""
        # Get available transitions
        url = f"{self.base_url}/rest/api/3/issue/{issue_key}/transitions"

        try:
            response = self.session.get(url)
            if response.status_code != 200:
                print(f"❌ Failed to get transitions: {response.status_code}")
                return False

            transitions = response.json()['transitions']

            # Find transition by name
            transition_id = None
            for transition in transitions:
                if transition['name'].lower() == target_status.lower():
                    transition_id = transition['id']
                    break
                elif transition['to']['name'].lower() == target_status.lower():
                    transition_id = transition['id']
                    break

            if not transition_id:
                print(f"❌ Transition '{target_status}' not found")
                return False

            # Execute transition
            payload = {"transition": {"id": transition_id}}
            response = self.session.post(url, json=payload)

            if response.status_code == 204:
                print(f"✅ Transitioned to: {target_status}")
                return True
            else:
                print(f"❌ Failed to transition: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Exception during transition: {e}")
            return False

# Task Templates
TASK_TEMPLATES = {
    'geometry_crawl': {
        'summary': 'Crawl geometry data cho xã phường',
        'description': 'Crawl dữ liệu geometry (tọa độ đa giác) cho tất cả xã phường từ API sapnhap.bando.com.vn và lưu vào bảng ward_geometry.',
        'parent_key': 'UBG-2790',
        'labels': ['UrGift', 'geometry', 'crawl', 'ward'],
        'tools': [
            'crawl_geometry.py - Tool chính crawl geometry cho tất cả xã',
            'crawl_single_geometry.py - Tool crawl geometry cho 1 xã cụ thể',
            'crawl_missing_geometry.py - Tool crawl các xã còn thiếu',
            'create_ward_geometry_table.sql - Script tạo bảng'
        ],
        'technical_details': [
            'API: https://sapnhap.bando.com.vn/pread_json',
            'Format ID: xa3321.{maxa+1}',
            'Output: JSON geometry data (GeoJSON format)',
            'Đã crawl: 3,312/3,321 records (còn thiếu 9 records có maxa=-1)'
        ],
        'auto_transition': 'In Progress'
    },

    'brand_office_coordinates': {
        'summary': 'Tạo tool cập nhật coordinates cho brand_office',
        'description': 'Đã tạo tool sử dụng Google Maps API để lấy latitude, longitude từ address_old và cập nhật vào bảng brand_office.',
        'parent_key': 'UBG-2790',
        'labels': ['UrGift', 'coordinates', 'geocoding', 'brand_office'],
        'tools': [
            'update_brand_office_coordinates.py - Tool chính với đầy đủ tính năng',
            'test_brand_office_db.py - Test database connection và kiểm tra dữ liệu',
            'GOOGLE_MAPS_API_SETUP.md - Hướng dẫn cấu hình Google Maps API',
            'README_BRAND_OFFICE_GEOCODING.md - Hướng dẫn sử dụng chi tiết'
        ],
        'technical_details': [
            'Google Maps Geocoding API integration',
            'Rate limiting và error handling',
            'Dry run mode để test trước khi cập nhật',
            'CSV export với báo cáo chi tiết',
            'Database direct update capability'
        ],
        'auto_transition': 'Done'
    }
}

def quick_upload(template_name, files=None, custom_data=None):
    """Quick function để upload với template có sẵn"""
    uploader = JiraAutoUploader()

    if template_name not in TASK_TEMPLATES:
        print(f"❌ Template '{template_name}' not found!")
        print(f"Available templates: {list(TASK_TEMPLATES.keys())}")
        return None

    # Get template data
    task_data = TASK_TEMPLATES[template_name].copy()

    # Override with custom data if provided
    if custom_data:
        task_data.update(custom_data)

    # Execute
    print(f"🚀 Auto-creating task with template: {template_name}")
    result = uploader.create_task_and_upload(task_data, files)

    # Summary
    if result['task_created']:
        print(f"\n🎉 SUCCESS!")
        print(f"📋 Task: {result['task_key']}")
        print(f"🔗 URL: https://urbox.atlassian.net/browse/{result['task_key']}")

        if result['files_uploaded']:
            successful = sum(1 for f in result['files_uploaded'] if f['success'])
            print(f"📎 Files uploaded: {successful}/{len(result['files_uploaded'])}")
    else:
        print(f"\n❌ FAILED!")
        for error in result['errors']:
            print(f"Error: {error}")

    return result

class JiraIntegration:
    def __init__(self):
        """Initialize Jira integration"""
        self.uploader = JiraAutoUploader()
        
        # File patterns for different types of work
        self.file_patterns = {
            'geometry_crawl': [
                'crawl_geometry.py',
                'crawl_single_geometry.py', 
                'crawl_missing_geometry.py',
                'create_ward_geometry_table.sql',
                'sample_geometry.json'
            ],
            'brand_office_coordinates': [
                'update_brand_office_coordinates.py',
                'test_brand_office_db.py',
                'GOOGLE_MAPS_API_SETUP.md',
                'README_BRAND_OFFICE_GEOCODING.md'
            ],
            'brand_store_coordinates': [
                'update_brand_store_coordinates.py',
                'test_brand_store_db.py',
                'README_BRAND_STORE_GEOCODING.md'
            ],
            'crawl_tools': [
                'crawl_all_xaphuong.py',
                'import_tinhthanh.py',
                'crawl_xaphuong_api.py'
            ],
            'duplicate_analysis': [
                'check_duplicate_maxa.py',
                'check_duplicate_xaphuong.py',
                'duplicate_*.csv'
            ],
            'jira_tools': [
                'jira_file_uploader.py',
                'jira_auto_uploader.py',
                'upload_geometry_files.py',
                'JIRA_API_TOKEN_SETUP.md',
                'README_JIRA_FILE_UPLOADER.md'
            ]
        }
    
    def auto_detect_files(self, work_type):
        """
        Tự động detect files dựa trên loại công việc
        
        Args:
            work_type (str): Loại công việc
            
        Returns:
            list: Danh sách files tồn tại
        """
        if work_type not in self.file_patterns:
            return []
        
        existing_files = []
        patterns = self.file_patterns[work_type]
        
        for pattern in patterns:
            if '*' in pattern:
                # Wildcard pattern
                matches = glob.glob(pattern)
                existing_files.extend(matches)
            else:
                # Exact file name
                if os.path.exists(pattern):
                    existing_files.append(pattern)
        
        return existing_files
    
    def upload_geometry_crawl_files(self, custom_summary=None):
        """Upload geometry crawl files to Jira"""
        files = self.auto_detect_files('geometry_crawl')
        
        custom_data = {}
        if custom_summary:
            custom_data['summary'] = custom_summary
        
        print(f"🎯 Uploading geometry crawl files...")
        print(f"📁 Files found: {len(files)}")
        for f in files:
            print(f"  - {f}")
        
        if not files:
            print("❌ No geometry crawl files found!")
            return None
        
        return quick_upload('geometry_crawl', files, custom_data)
    
    def upload_brand_office_files(self, custom_summary=None):
        """Upload brand office coordinate files to Jira"""
        files = self.auto_detect_files('brand_office_coordinates')
        
        custom_data = {}
        if custom_summary:
            custom_data['summary'] = custom_summary
        
        print(f"🎯 Uploading brand office coordinate files...")
        print(f"📁 Files found: {len(files)}")
        for f in files:
            print(f"  - {f}")
        
        if not files:
            print("❌ No brand office files found!")
            return None
        
        return quick_upload('brand_office_coordinates', files, custom_data)
    
    def upload_jira_tools(self):
        """Upload Jira tools to a new task"""
        files = self.auto_detect_files('jira_tools')
        
        # Custom task data for Jira tools
        task_data = {
            'summary': 'Tạo tool upload file lên Jira',
            'description': 'Đã tạo bộ tool hoàn chỉnh để upload file lên Jira issues một cách tự động.',
            'parent_key': 'UBG-2790',
            'labels': ['UrGift', 'jira', 'upload', 'automation'],
            'tools': [
                'jira_file_uploader.py - Tool chính upload file với interactive menu',
                'jira_auto_uploader.py - Tool tự động tạo task và upload',
                'upload_geometry_files.py - Script chuyên biệt cho geometry files',
                'JIRA_API_TOKEN_SETUP.md - Hướng dẫn tạo API token',
                'README_JIRA_FILE_UPLOADER.md - Hướng dẫn sử dụng chi tiết'
            ],
            'technical_details': [
                'Jira REST API v3 integration',
                'Batch file upload với progress tracking',
                'Pre-configured templates cho các loại task',
                'Auto task creation và transition',
                'Error handling và retry mechanism'
            ],
            'auto_transition': 'Done'
        }
        
        print(f"🎯 Uploading Jira tools...")
        print(f"📁 Files found: {len(files)}")
        for f in files:
            print(f"  - {f}")
        
        if not files:
            print("❌ No Jira tool files found!")
            return None
        
        result = self.uploader.create_task_and_upload(task_data, files)
        
        # Summary
        if result['task_created']:
            print(f"\n🎉 SUCCESS!")
            print(f"📋 Task: {result['task_key']}")
            print(f"🔗 URL: https://urbox.atlassian.net/browse/{result['task_key']}")
            
            if result['files_uploaded']:
                successful = sum(1 for f in result['files_uploaded'] if f['success'])
                print(f"📎 Files uploaded: {successful}/{len(result['files_uploaded'])}")
        
        return result
    
    def upload_custom_files(self, task_summary, files, description=None, parent_key="UBG-2790"):
        """
        Upload custom files với task summary tự định
        
        Args:
            task_summary (str): Tên task
            files (list): Danh sách file paths
            description (str): Mô tả task
            parent_key (str): Parent epic key
        """
        # Filter existing files
        existing_files = [f for f in files if os.path.exists(f)]
        missing_files = [f for f in files if not os.path.exists(f)]
        
        if missing_files:
            print(f"⚠️ Missing files: {', '.join(missing_files)}")
        
        if not existing_files:
            print("❌ No files to upload!")
            return None
        
        # Build task data
        task_data = {
            'summary': task_summary,
            'description': description or f"Upload files cho task: {task_summary}",
            'parent_key': parent_key,
            'labels': ['UrGift', 'file_upload'],
            'auto_transition': 'Done'
        }
        
        print(f"🎯 Creating custom task: {task_summary}")
        print(f"📁 Files to upload: {len(existing_files)}")
        for f in existing_files:
            print(f"  - {f}")
        
        result = self.uploader.create_task_and_upload(task_data, existing_files)
        
        # Summary
        if result['task_created']:
            print(f"\n🎉 SUCCESS!")
            print(f"📋 Task: {result['task_key']}")
            print(f"🔗 URL: https://urbox.atlassian.net/browse/{result['task_key']}")
            
            if result['files_uploaded']:
                successful = sum(1 for f in result['files_uploaded'] if f['success'])
                print(f"📎 Files uploaded: {successful}/{len(result['files_uploaded'])}")
        
        return result

# Global instance for easy access
jira_integration = JiraIntegration()

# Convenience functions
def upload_geometry_files():
    """Quick function to upload geometry crawl files"""
    return jira_integration.upload_geometry_crawl_files()

def upload_brand_office_files():
    """Quick function to upload brand office files"""
    return jira_integration.upload_brand_office_files()

def upload_jira_tools():
    """Quick function to upload Jira tools"""
    return jira_integration.upload_jira_tools()

def upload_files(task_summary, files, description=None):
    """Quick function to upload custom files"""
    return jira_integration.upload_custom_files(task_summary, files, description)

def main():
    """Demo usage"""
    print("🔧 JIRA INTEGRATION DEMO")
    print("=" * 60)
    
    print("Available functions:")
    print("1. upload_geometry_files() - Upload geometry crawl files")
    print("2. upload_brand_office_files() - Upload brand office files") 
    print("3. upload_jira_tools() - Upload Jira tools")
    print("4. upload_files(summary, files) - Upload custom files")
    
    # Example usage:
    # result = upload_geometry_files()
    # result = upload_brand_office_files()
    # result = upload_jira_tools()
    # result = upload_files("Custom Task", ["file1.py", "file2.sql"])

if __name__ == "__main__":
    main()
