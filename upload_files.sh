#!/bin/bash

# Upload geometry crawl files to UBG-2816
# Usage: ./upload_files.sh

JIRA_URL="https://urbox.atlassian.net"
ISSUE_KEY="UBG-2816"
EMAIL="<EMAIL>"
API_TOKEN="ATATT3xFfGF0W9N5Xovi_R3lZ1PZKOU21ZqCjIjle74xJui2tEeXpIFz-6q7cPRAC9I52rCww26E2dwmNlvPINe_fBwsqbMkoGhgMsJbLwxFpbOdaHBb-Lptk5M1BydFujufdv7T7fqi2SsWZicnizGMbQibBaRkac7nfW2C4nqTiqWLS6rrVY0=548B34C7"

# Files to upload
FILES=(
    "crawl_geometry.py"
    "crawl_single_geometry.py"
    "crawl_missing_geometry.py"
    "create_ward_geometry_table.sql"
    "sample_geometry.json"
)

echo "🚀 UPLOADING GEOMETRY CRAWL FILES TO $ISSUE_KEY"
echo "============================================================"

# Check which files exist
EXISTING_FILES=()
MISSING_FILES=()

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        EXISTING_FILES+=("$file")
        size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "unknown")
        echo "✅ Found: $file ($size bytes)"
    else
        MISSING_FILES+=("$file")
        echo "❌ Missing: $file"
    fi
done

echo ""
echo "📊 Summary: ${#EXISTING_FILES[@]} found, ${#MISSING_FILES[@]} missing"

if [ ${#EXISTING_FILES[@]} -eq 0 ]; then
    echo "❌ No files to upload!"
    exit 1
fi

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "⚠️ Missing files: ${MISSING_FILES[*]}"
fi

echo ""
echo "🎯 Target issue: $ISSUE_KEY"
echo "📋 Files to upload: ${#EXISTING_FILES[@]}"
echo ""

# Upload files
SUCCESSFUL=0
FAILED=0

for file in "${EXISTING_FILES[@]}"; do
    echo "📎 Uploading: $file"
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "X-Atlassian-Token: no-check" \
        -F "file=@$file" \
        -u "$EMAIL:$API_TOKEN" \
        "$JIRA_URL/rest/api/3/issue/$ISSUE_KEY/attachments")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        echo "  ✅ Success (HTTP $http_code)"
        ((SUCCESSFUL++))
    else
        echo "  ❌ Failed (HTTP $http_code)"
        echo "  Response: $response_body"
        ((FAILED++))
    fi
    echo ""
done

# Summary
echo "============================================================"
echo "📊 UPLOAD SUMMARY"
echo "============================================================"
echo "✅ Successful: $SUCCESSFUL"
echo "❌ Failed: $FAILED"
echo "📋 Total: ${#EXISTING_FILES[@]}"

if [ $SUCCESSFUL -gt 0 ]; then
    echo ""
    echo "🔗 View issue: $JIRA_URL/browse/$ISSUE_KEY"
    echo "🎉 Upload completed!"
else
    echo ""
    echo "❌ All uploads failed!"
    exit 1
fi
