# Brand Office Geocoding Tool

Tool để cập nhật latitude, longitude cho bảng `brand_office` sử dụng Google Maps Geocoding API.

## 📁 Files đã tạo

1. **`update_brand_office_coordinates.py`** - Tool chính để geocoding
2. **`test_brand_office_db.py`** - Test kết nối database và kiểm tra dữ liệu
3. **`GOOGLE_MAPS_API_SETUP.md`** - Hướng dẫn cấu hình Google Maps API
4. **`README_BRAND_OFFICE_GEOCODING.md`** - File này

## 🚀 Cách sử dụng

### Bước 1: Test database
```bash
python3 test_brand_office_db.py
```

### Bước 2: Cấu hình Google Maps API
- Đọc file `GOOGLE_MAPS_API_SETUP.md`
- Tạo API key từ Google Cloud Console
- Kích hoạt Geocoding API

### Bước 3: Chạy tool geocoding
```bash
python3 update_brand_office_coordinates.py
```

## 🎯 Tính năng

### Tool chính (`update_brand_office_coordinates.py`)
- ✅ Lấy dữ liệu từ bảng `brand_office`
- ✅ Sử dụng Google Maps Geocoding API
- ✅ Cập nhật latitude, longitude vào database
- ✅ Rate limiting để tránh bị block
- ✅ Retry mechanism cho failed requests
- ✅ Dry run mode để test
- ✅ Export kết quả ra CSV
- ✅ Error handling và logging

### Tool test (`test_brand_office_db.py`)
- ✅ Test kết nối database
- ✅ Kiểm tra cấu trúc bảng brand_office
- ✅ Thống kê dữ liệu
- ✅ Hiển thị records mẫu cần geocoding

## 📊 Cấu trúc database yêu cầu

```sql
-- Bảng brand_office cần có các trường:
CREATE TABLE brand_office (
    id INT PRIMARY KEY,
    name VARCHAR(255),           -- Tên văn phòng
    address_old TEXT,            -- Địa chỉ cần geocoding
    latitude DECIMAL(10,8),      -- Latitude (sẽ được cập nhật)
    longitude DECIMAL(11,8),     -- Longitude (sẽ được cập nhật)
    -- other fields...
);
```

## ⚙️ Cấu hình

### Database
```python
db_config = {
    'user': 'root',
    'password': 'root',
    'host': 'localhost',
    'port': '3306',
    'database': 'urbox'
}
```

### Google Maps API
- Cần API key hợp lệ
- Kích hoạt Geocoding API
- Có thể set rate limiting

## 🔧 Options khi chạy tool

1. **Test với 5 records (dry run)** - Test không cập nhật DB
2. **Test với 10 records (dry run)** - Test với nhiều records hơn
3. **Cập nhật thực tế 10 records** - Cập nhật thật vào DB
4. **Cập nhật thực tế tất cả** - Cập nhật toàn bộ
5. **Nhập API key và chạy** - Nhập API key runtime

## 📈 Output

### Console output
```
🚀 BẮT ĐẦU CẬP NHẬT COORDINATES CHO BRAND_OFFICE
============================================================
🔍 Lấy dữ liệu brand_office...
   Tìm thấy 25 brand_office cần cập nhật coordinates
📋 Sẽ xử lý 25 brand_office

[1/25] 🏢 Văn phòng Hà Nội
   📍 Địa chỉ: 123 Nguyễn Huệ, Quận 1, TP.HCM
   📊 Coordinates cũ: None, None
   🌍 Geocoding: 123 Nguyễn Huệ, Quận 1, TP.HCM...
   ✅ Found: 10.7769, 106.7009
   ✅ Đã cập nhật coordinates: 10.7769, 106.7009
```

### CSV output
File: `brand_office_geocoding_YYYYMMDD_HHMMSS.csv`

| office_id | name | address_old | old_latitude | old_longitude | new_latitude | new_longitude | status | error_message |
|-----------|------|-------------|--------------|---------------|--------------|---------------|--------|---------------|
| 1 | Văn phòng HN | 123 Nguyễn Huệ... | NULL | NULL | 10.7769 | 106.7009 | Success | |
| 2 | Văn phòng HCM | 456 Lê Lợi... | 0 | 0 | 10.7829 | 106.6934 | Success | |

## 🛡️ Error Handling

### API Errors
- **OVER_QUERY_LIMIT**: Tự động retry với delay
- **ZERO_RESULTS**: Log và skip
- **REQUEST_DENIED**: Check API key
- **INVALID_REQUEST**: Check address format

### Database Errors
- Connection timeout
- Query syntax errors
- Update failures

## 💰 Chi phí Google Maps API

- **Geocoding API**: $5 per 1,000 requests
- **Free tier**: $200 credit/tháng (≈ 40,000 requests)
- **Rate limit**: 50 requests/second (default)

## 🔒 Security

- Không commit API key vào git
- Sử dụng environment variables
- Restrict API key theo IP/domain
- Monitor usage thường xuyên

## 🐛 Troubleshooting

### Lỗi kết nối database
```bash
# Kiểm tra MySQL service
sudo service mysql status

# Test kết nối manual
mysql -u root -proot -h localhost -P 3306 -D urbox
```

### Lỗi API key
- Kiểm tra API key trong Google Cloud Console
- Verify Geocoding API đã được enable
- Check billing account

### Lỗi địa chỉ không tìm thấy
- Kiểm tra format địa chỉ
- Thêm thông tin thành phố, quận/huyện
- Sử dụng tiếng Việt có dấu

## 📝 Logs và Monitoring

### Console logs
- Real-time progress
- Success/failure cho từng record
- Summary statistics

### CSV reports
- Detailed results cho mỗi record
- Error messages
- Before/after coordinates

### Google Cloud Console
- API usage metrics
- Error rates
- Billing information

## 🔄 Workflow khuyến nghị

1. **Test database connection**
   ```bash
   python3 test_brand_office_db.py
   ```

2. **Dry run với sample data**
   ```bash
   python3 update_brand_office_coordinates.py
   # Chọn option 1 hoặc 2
   ```

3. **Cấu hình API key**
   - Tạo API key từ Google Cloud Console
   - Test với option 5

4. **Production run**
   ```bash
   python3 update_brand_office_coordinates.py
   # Chọn option 3 hoặc 4
   ```

5. **Verify results**
   - Check CSV output
   - Verify database updates
   - Monitor API usage

## 📞 Support

Nếu gặp vấn đề:
1. Check logs trong console
2. Verify database connection
3. Check Google Cloud Console cho API errors
4. Review CSV output cho detailed errors
