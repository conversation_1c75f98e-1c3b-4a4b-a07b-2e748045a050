#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.request
import urllib.parse
import json
import time
import subprocess
import random
from datetime import datetime

class MissingGeometryCrawler:
    def __init__(self):
        self.base_url = "https://sapnhap.bando.com.vn/pread_json"
        self.headers = {
            'Accept': 'text/plain, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Cookie': 'PHPSESSID=tncekeavtrlr9jtequvls32mtl',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
    def get_missing_maxa_list(self):
        """L<PERSON>y danh sách maxa còn thiếu geometry"""
        print("🔍 Tìm các maxa còn thiếu geometry...")
        
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', 
                '''SELECT x.maxa, x.tenhc, x.matinh
                FROM xaphuong x
                WHERE x.maxa IS NOT NULL 
                AND x.maxa NOT IN (SELECT pti_id FROM ward_geometry WHERE pti_id IS NOT NULL)
                ORDER BY x.maxa'''
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Lỗi query database: {result.stderr}")
                return []
            
            # Parse kết quả
            lines = result.stdout.strip().split('\n')
            missing_list = []
            
            for line in lines[1:]:  # Bỏ header
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        try:
                            missing_record = {
                                'maxa': int(parts[0]),
                                'tenhc': parts[1],
                                'matinh': int(parts[2])
                            }
                            missing_list.append(missing_record)
                        except ValueError:
                            continue
            
            print(f"✅ Tìm thấy {len(missing_list)} maxa còn thiếu geometry")
            return missing_list
            
        except Exception as e:
            print(f"❌ Lỗi get missing maxa: {e}")
            return []
    
    def crawl_geometry_data(self, api_id):
        """Crawl geometry data cho một api_id (maxa + 1)"""
        # Tạo ID theo format xa3321.{api_id}
        geometry_id = f"xa3321.{api_id}"
        
        print(f"🔄 Crawling geometry cho ID: {geometry_id}")
        
        try:
            # Tạo POST data
            data = urllib.parse.urlencode({'id': geometry_id}).encode('utf-8')
            
            # Tạo request
            req = urllib.request.Request(
                self.base_url,
                data=data,
                headers=self.headers
            )
            
            # Gửi request
            with urllib.request.urlopen(req, timeout=30) as response:
                if response.getcode() != 200:
                    print(f"❌ HTTP Error: {response.getcode()}")
                    return None
                
                # Đọc response
                response_data = response.read().decode('utf-8')
                
                # Kiểm tra response có hợp lệ không
                if not response_data or response_data.strip() == '':
                    print(f"⚠️ Response rỗng cho ID: {geometry_id}")
                    return None
                
                # Thử parse JSON để kiểm tra tính hợp lệ
                try:
                    json.loads(response_data)
                    print(f"✅ Crawl thành công geometry cho ID: {geometry_id}")
                    return response_data
                except json.JSONDecodeError:
                    print(f"⚠️ Response không phải JSON hợp lệ cho ID: {geometry_id}")
                    return response_data  # Vẫn lưu dù không phải JSON
                
        except urllib.error.URLError as e:
            print(f"❌ URL Error cho ID {geometry_id}: {e}")
            return None
        except Exception as e:
            print(f"❌ Exception cho ID {geometry_id}: {e}")
            return None
    
    def save_geometry_to_db(self, maxa, geometry_data):
        """Lưu geometry data vào database với pti_id = maxa (giá trị gốc)"""
        if not geometry_data:
            return False
        
        try:
            # Escape JSON data cho SQL
            escaped_data = geometry_data.replace("'", "\\'").replace('"', '\\"')
            
            insert_sql = f"""INSERT INTO ward_geometry (pti_id, data) VALUES ({maxa}, '{escaped_data}')"""
            
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', insert_sql
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return True
            else:
                print(f"❌ Lỗi insert geometry: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Exception save geometry: {e}")
            return False
    
    def crawl_missing_geometry(self, delay_range=(0.2, 1)):
        """Crawl geometry cho các maxa còn thiếu"""
        print("🚀 BẮT ĐẦU CRAWL GEOMETRY CHO CÁC MAXA THIẾU")
        print("=" * 60)
        
        # Lấy danh sách maxa thiếu
        missing_list = self.get_missing_maxa_list()
        if not missing_list:
            print("✅ Không có maxa nào thiếu geometry!")
            return
        
        print(f"📋 Sẽ crawl geometry cho {len(missing_list)} maxa thiếu")
        print(f"⏱️ Delay giữa requests: {delay_range[0]}-{delay_range[1]} giây")
        print()
        
        # Thống kê
        total_success = 0
        total_failed = 0
        
        for i, record in enumerate(missing_list, 1):
            maxa = record['maxa']
            tenhc = record['tenhc']
            matinh = record['matinh']
            api_id = maxa + 1  # maxa + 1 để gọi API
            
            print(f"[{i}/{len(missing_list)}] 🏘️ {tenhc} (matinh={matinh}, maxa={maxa}, api_id={api_id})")
            
            # Crawl geometry data
            try:
                geometry_data = self.crawl_geometry_data(api_id)
                
                if geometry_data:
                    # Lưu vào database
                    if self.save_geometry_to_db(maxa, geometry_data):
                        total_success += 1
                        print(f"✅ Thành công!")
                    else:
                        total_failed += 1
                        print(f"❌ Lỗi lưu database!")
                else:
                    total_failed += 1
                    print(f"❌ Không lấy được geometry data!")
                
                # Random delay để tránh bị block
                if i < len(missing_list):
                    delay = random.uniform(delay_range[0], delay_range[1])
                    print(f"⏳ Chờ {delay:.1f} giây...")
                    time.sleep(delay)
                    
            except Exception as e:
                print(f"❌ Exception: {e}")
                total_failed += 1
            
            print()
        
        # Báo cáo kết quả
        print("=" * 60)
        print("📊 KẾT QUẢ CRAWL GEOMETRY THIẾU")
        print("=" * 60)
        print(f"✅ Thành công: {total_success}")
        print(f"❌ Thất bại: {total_failed}")
        print(f"📋 Tổng cộng: {len(missing_list)}")
        
        # Kiểm tra lại số geometry data sau khi crawl
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', 
                'SELECT COUNT(*) as total FROM ward_geometry'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    total_records = lines[1]
                    print(f"📊 Tổng số geometry data trong DB: {total_records}")
        except:
            pass

def main():
    """Hàm main"""
    print("🔧 CRAWL MISSING GEOMETRY DATA TOOL")
    print("=" * 60)
    
    crawler = MissingGeometryCrawler()
    crawler.crawl_missing_geometry()

if __name__ == "__main__":
    main()
