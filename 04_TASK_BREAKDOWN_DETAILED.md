# TASK BREAKDOWN CHI TIẾT
## D<PERSON> án Cập nhật Hệ thống Hành chính Việt Nam

**Tổng quan:** 21 tasks, 5 hoàn thành, 16 còn lại
**Ngày cập nhật:** 13/01/2025

---

## ✅ COMPLETED TASKS (5/21)

### 🗂️ NHÓM CRAWL DỮ LIỆU

| Task | Mô tả | Trạng thái | Kết quả | Tools |
|------|-------|------------|---------|-------|
| **1** | Crawl dữ liệu tỉnh thành mới | ✅ **DONE** | 34 records → `tinhthanh` | `import_tinhthanh.py` |
| **2** | Crawl dữ liệu xã phường mới | ✅ **DONE** | 3,321 records → `xaphuong` | `crawl_all_xaphuong.py` |
| **3** | Crawl geometry tỉnh thành | ✅ **DONE** | Included in task 4 | - |
| **4** | Crawl geometry xã phường | ✅ **DONE** | 3,312 records → `ward_geometry` | `crawl_geometry.py` |

### 🏢 NHÓM COORDINATES UPDATE

| Task | Mô tả | Trạng thái | Kết quả | Tools |
|------|-------|------------|---------|-------|
| **7** | Cập nhật coordinates brand_office | ✅ **DONE** | Tool Google Maps API | `update_brand_office_coordinates.py` |

---

## 📋 PENDING TASKS (16/21)

### 🗄️ NHÓM DATABASE (Tasks 5-14)

#### **Task 5** - Province Table Update
**Mô tả:** Bảng province chứa tỉnh cũ và mới, thêm cột new_province_id và is_merge  
**Độ ưu tiên:** 🔴 HIGH  
**Ước tính:** 2 ngày  
**Dependencies:** Tasks 1-2 hoàn thành ✅

**Chi tiết kỹ thuật:**
```sql
ALTER TABLE province ADD COLUMN (
  new_province_id INT,
  is_merge TINYINT DEFAULT 1 COMMENT '1=old, 2=new'
);
```

**Acceptance Criteria:**
- [ ] 34 tỉnh mới có is_merge=2
- [ ] Tỉnh cũ được map đến tỉnh mới qua new_province_id
- [ ] Queries filter by is_merge=2 cho dữ liệu mới

---

#### **Task 6** - Ward Table Update  
**Mô tả:** Bảng ward chứa xã cũ và mới, mapping xã cũ → xã mới  
**Độ ưu tiên:** 🔴 HIGH  
**Ước tính:** 3 ngày  
**Dependencies:** Task 5

**Chi tiết kỹ thuật:**
```sql
ALTER TABLE ward ADD COLUMN (
  new_ward_id INT,
  is_merge TINYINT DEFAULT 1 COMMENT '1=old, 2=new'
);
```

**Thách thức:** Cần business logic để map xã cũ → xã mới

---

#### **Task 8** - Brand Store Coordinate Sync
**Mô tả:** Đồng bộ lại dữ liệu lat, long cho brand_store  
**Độ ưu tiên:** 🟡 MEDIUM  
**Ước tính:** 1 ngày  
**Dependencies:** Task 7 (same approach)

---

#### **Task 9** - Brand Office Address Update
**Mô tả:** Sử dụng geometry để cập nhật địa chỉ trong cột title  
**Độ ưu tiên:** 🟠 MEDIUM-HIGH  
**Ước tính:** 3 ngày  
**Dependencies:** Tasks 4, 7

**Logic:**
```
Current: "123 Nguyễn Văn A, Phường B, Quận C, Tỉnh D"
Target:  "123 Nguyễn Văn A, Phường B, Tỉnh D"
```

**Technical Approach:**
1. Parse existing title field
2. Point-in-polygon check using ward_geometry  
3. Reconstruct title with new structure

---

#### **Task 10** - Brand Store Address Update
**Mô tả:** Tương tự Task 9 cho brand_store  
**Độ ưu tiên:** 🟠 MEDIUM-HIGH  
**Ước tính:** 2 ngày  
**Dependencies:** Tasks 4, 8, 9

---

#### **Task 11** - Gift Receiver Migration Flag
**Mô tả:** Thêm cột đánh dấu sử dụng dữ liệu mới  
**Độ ưu tiên:** 🟢 LOW  
**Ước tính:** 0.5 ngày  
**Dependencies:** None

```sql
ALTER TABLE gift_receiver ADD COLUMN use_new_structure BOOLEAN DEFAULT FALSE;
```

---

#### **Task 12** - Address Migration Flag
**Mô tả:** Thêm cột đánh dấu sử dụng dữ liệu mới cho bảng address  
**Độ ưu tiên:** 🟢 LOW  
**Ước tính:** 0.5 ngày  
**Dependencies:** None

---

#### **Task 13** - Cart Analysis
**Mô tả:** Đang phân tích requirements cho bảng cart  
**Độ ưu tiên:** 🔵 TBD  
**Ước tính:** TBD  
**Dependencies:** Business analysis

---

#### **Task 14** - Cart Detail Analysis  
**Mô tả:** Đang phân tích requirements cho bảng cart_detail  
**Độ ưu tiên:** 🔵 TBD  
**Ước tính:** TBD  
**Dependencies:** Task 13

---

### 🔌 NHÓM API (Tasks 15-21)

#### **Task 15** - API Danh sách Tỉnh Mới
**Mô tả:** Viết API lấy danh sách tỉnh mới  
**Độ ưu tiên:** 🔴 HIGH  
**Ước tính:** 1 ngày  
**Dependencies:** Task 5

**Endpoint:** `GET /api/provinces`
```json
{
  "data": [
    {
      "id": 1,
      "mahc": 1, 
      "name": "Thủ đô Hà Nội",
      "coordinates": [105.698, 21.0001]
    }
  ]
}
```

---

#### **Task 16** - API Danh sách Xã theo Tỉnh
**Mô tả:** Viết API lấy danh sách xã dựa trên ID tỉnh  
**Độ ưu tiên:** 🔴 HIGH  
**Ước tính:** 1 ngày  
**Dependencies:** Task 6

**Endpoint:** `GET /api/provinces/{id}/wards`

---

#### **Task 17** - Cập nhật Whitelabel
**Mô tả:** Cập nhật whitelabel system với dữ liệu mới  
**Độ ưu tiên:** 🟠 MEDIUM-HIGH  
**Ước tính:** 2 ngày  
**Dependencies:** Tasks 15-16

---

#### **Task 18** - API Đối tác CPV
**Mô tả:** Cập nhật API phục vụ đối tác CPV  
**Độ ưu tiên:** 🟠 MEDIUM-HIGH  
**Ước tính:** 2 ngày  
**Dependencies:** Tasks 15-16

---

#### **Task 19** - API Đối tác NHANH
**Mô tả:** Cập nhật API gọi sang đối tác NHANH  
**Độ ưu tiên:** 🟠 MEDIUM-HIGH  
**Ước tính:** 2 ngày  
**Dependencies:** Tasks 15-16

---

#### **Task 20** - API cho APP
**Mô tả:** Cập nhật API cho mobile application  
**Độ ưu tiên:** 🔴 HIGH  
**Ước tính:** 2 ngày  
**Dependencies:** Tasks 15-16

---

#### **Task 21** - API Giftlink
**Mô tả:** Cập nhật API giftlink với dữ liệu mới  
**Độ ưu tiên:** 🟡 MEDIUM  
**Ước tính:** 1 ngày  
**Dependencies:** Tasks 15-16

---

## 📊 SUMMARY BY PRIORITY

### 🔴 HIGH Priority (6 tasks - 11 ngày):
- Task 5: Province table update (2 ngày)
- Task 6: Ward table update (3 ngày)  
- Task 15: API tỉnh mới (1 ngày)
- Task 16: API xã theo tỉnh (1 ngày)
- Task 20: API cho APP (2 ngày)

### 🟠 MEDIUM-HIGH Priority (5 tasks - 11 ngày):
- Task 9: Brand office address update (3 ngày)
- Task 10: Brand store address update (2 ngày)
- Task 17: Whitelabel update (2 ngày)
- Task 18: API CPV (2 ngày)
- Task 19: API NHANH (2 ngày)

### 🟡 MEDIUM Priority (2 tasks - 2 ngày):
- Task 8: Brand store coordinates (1 ngày)
- Task 21: API Giftlink (1 ngày)

### 🟢 LOW Priority (2 tasks - 1 ngày):
- Task 11: Gift receiver flag (0.5 ngày)
- Task 12: Address flag (0.5 ngày)

### 🔵 TBD (2 tasks):
- Task 13: Cart analysis
- Task 14: Cart detail analysis

---

## 🗓️ SUGGESTED TIMELINE

### Week 1 (13-17/01): Database Foundation
- Days 1-2: Task 5 (Province update)
- Days 3-5: Task 6 (Ward update)

### Week 2 (20-24/01): Core APIs  
- Days 1-2: Tasks 15-16 (Province/Ward APIs)
- Days 3-5: Task 20 (Mobile API)

### Week 3 (27-31/01): Business Logic
- Day 1: Task 8 (Brand store coordinate sync)
- Days 2-5: Tasks 9-10 (Address updates)

### Week 4 (03-07/02): Partner Integration
- Days 1-3: Tasks 17-19 (Whitelabel, CPV, NHANH)
- Days 4-5: Tasks 11-12, 21 (Flags, Giftlink)

**Total Estimate:** 25 working days (~5.0 weeks)

---

*Task breakdown được chuẩn bị bởi: Project Management Office*  
*Cập nhật: 13/01/2025*
