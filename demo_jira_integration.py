#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Demo script showing how to use Jira Integration
Đây là cách Augment Agent sẽ sử dụng tool khi user yêu cầu upload file
"""

from jira_integration import (
    upload_geometry_files,
    upload_brand_office_files, 
    upload_jira_tools,
    upload_files
)

def demo_usage():
    """Demo các cách sử dụng tool"""
    print("🔧 DEMO JIRA INTEGRATION USAGE")
    print("=" * 60)
    
    print("\n1. 📊 Upload geometry crawl files:")
    print("   Khi user nói: 'upload geometry crawl files lên jira'")
    print("   Agent sẽ gọi: upload_geometry_files()")
    print("   → Tự động tạo task UBG-xxxx và upload các file:")
    print("     - crawl_geometry.py")
    print("     - crawl_single_geometry.py") 
    print("     - crawl_missing_geometry.py")
    print("     - create_ward_geometry_table.sql")
    print("     - sample_geometry.json")
    
    print("\n2. 🏢 Upload brand office coordinate files:")
    print("   Khi user nói: 'upload brand office tool lên jira'")
    print("   Agent sẽ gọi: upload_brand_office_files()")
    print("   → Tự động tạo task UBG-xxxx và upload các file:")
    print("     - update_brand_office_coordinates.py")
    print("     - test_brand_office_db.py")
    print("     - GOOGLE_MAPS_API_SETUP.md")
    print("     - README_BRAND_OFFICE_GEOCODING.md")
    
    print("\n3. 🔧 Upload Jira tools:")
    print("   Khi user nói: 'upload jira tools lên jira'")
    print("   Agent sẽ gọi: upload_jira_tools()")
    print("   → Tự động tạo task UBG-xxxx và upload các file:")
    print("     - jira_file_uploader.py")
    print("     - jira_auto_uploader.py")
    print("     - upload_geometry_files.py")
    print("     - JIRA_API_TOKEN_SETUP.md")
    print("     - README_JIRA_FILE_UPLOADER.md")
    
    print("\n4. 📁 Upload custom files:")
    print("   Khi user nói: 'upload file1.py và file2.sql lên jira với tên task ABC'")
    print("   Agent sẽ gọi: upload_files('ABC', ['file1.py', 'file2.sql'])")
    print("   → Tự động tạo task với tên 'ABC' và upload files")

def example_agent_responses():
    """Ví dụ cách Agent sẽ respond khi user yêu cầu"""
    print("\n" + "=" * 60)
    print("🤖 EXAMPLE AGENT RESPONSES")
    print("=" * 60)
    
    scenarios = [
        {
            'user_request': 'upload geometry crawl files lên jira',
            'agent_action': 'upload_geometry_files()',
            'description': 'Tự động detect và upload tất cả file liên quan đến geometry crawl'
        },
        {
            'user_request': 'upload brand office coordinate tool lên jira', 
            'agent_action': 'upload_brand_office_files()',
            'description': 'Tự động detect và upload tất cả file liên quan đến brand office coordinates'
        },
        {
            'user_request': 'upload jira uploader tool lên jira',
            'agent_action': 'upload_jira_tools()',
            'description': 'Tự động detect và upload tất cả file liên quan đến Jira tools'
        },
        {
            'user_request': 'upload file crawl_data.py và report.md lên jira với tên "Data Analysis Task"',
            'agent_action': 'upload_files("Data Analysis Task", ["crawl_data.py", "report.md"])',
            'description': 'Upload custom files với task name tự định'
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. User: \"{scenario['user_request']}\"")
        print(f"   Agent calls: {scenario['agent_action']}")
        print(f"   Result: {scenario['description']}")

def setup_instructions():
    """Hướng dẫn setup cho Agent"""
    print("\n" + "=" * 60)
    print("⚙️ SETUP INSTRUCTIONS FOR AGENT")
    print("=" * 60)
    
    print("\n1. 📋 Copy config template:")
    print("   cp jira_config_template.py jira_config.py")
    
    print("\n2. ✏️ Edit jira_config.py with actual credentials:")
    print("   - base_url: https://urbox.atlassian.net")
    print("   - email: <EMAIL>")
    print("   - api_token: YOUR_ACTUAL_API_TOKEN")
    print("   - default_assignee: YOUR_ACCOUNT_ID")
    
    print("\n3. 🔑 Get API token from:")
    print("   https://id.atlassian.com/manage-profile/security/api-tokens")
    
    print("\n4. 🧪 Test the integration:")
    print("   python3 demo_jira_integration.py")
    
    print("\n5. 🚀 Use in Agent workflow:")
    print("   from jira_integration import upload_geometry_files")
    print("   result = upload_geometry_files()")

def test_integration():
    """Test integration (requires valid config)"""
    print("\n" + "=" * 60)
    print("🧪 TESTING INTEGRATION")
    print("=" * 60)
    
    try:
        from jira_config import validate_config
        is_valid, message = validate_config()
        
        if is_valid:
            print("✅ Configuration is valid")
            print("🚀 Ready to upload files to Jira!")
            
            # Uncomment to actually test upload
            # print("\nTesting geometry files upload...")
            # result = upload_geometry_files()
            # if result and result['task_created']:
            #     print(f"✅ Test successful! Task: {result['task_key']}")
            # else:
            #     print("❌ Test failed!")
            
        else:
            print(f"❌ Configuration error: {message}")
            print("Please setup jira_config.py first")
            
    except ImportError:
        print("❌ jira_config.py not found")
        print("Please copy jira_config_template.py to jira_config.py and configure it")

def main():
    """Main demo function"""
    demo_usage()
    example_agent_responses()
    setup_instructions()
    test_integration()

if __name__ == "__main__":
    main()
