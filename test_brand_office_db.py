#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess

class DatabaseTester:
    def __init__(self):
        self.db_config = {
            'user': 'root',
            'password': 'root',
            'host': 'localhost',
            'port': '3306',
            'database': 'urbox'
        }
    
    def execute_query(self, query):
        """Thực thi query MySQL và trả về kết quả"""
        try:
            cmd = [
                'mysql', '-u', self.db_config['user'], 
                f"-p{self.db_config['password']}", 
                '-h', self.db_config['host'], 
                '-P', self.db_config['port'],
                '-D', self.db_config['database'], 
                '-e', query
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Lỗi query: {result.stderr}")
                return None
            
            return result.stdout.strip()
            
        except Exception as e:
            print(f"❌ Exception execute query: {e}")
            return None
    
    def test_connection(self):
        """Test kết nối database"""
        print("🔍 Test kết nối database...")
        
        query = "SELECT 1 as test"
        result = self.execute_query(query)
        
        if result:
            print("✅ Kết nối database thành công")
            return True
        else:
            print("❌ Không thể kết nối database")
            return False
    
    def check_brand_office_table(self):
        """Kiểm tra bảng brand_office"""
        print("🔍 Kiểm tra bảng brand_office...")
        
        # Kiểm tra bảng có tồn tại không
        query = "SHOW TABLES LIKE 'brand_office'"
        result = self.execute_query(query)
        
        if not result or 'brand_office' not in result:
            print("❌ Bảng brand_office không tồn tại")
            return False
        
        print("✅ Bảng brand_office tồn tại")
        
        # Kiểm tra cấu trúc bảng
        print("\n📋 Cấu trúc bảng brand_office:")
        query = "DESCRIBE brand_office"
        result = self.execute_query(query)
        
        if result:
            print(result)
        
        return True
    
    def check_data_sample(self):
        """Kiểm tra dữ liệu mẫu"""
        print("\n🔍 Kiểm tra dữ liệu mẫu...")
        
        # Đếm tổng số records
        query = "SELECT COUNT(*) as total FROM brand_office"
        result = self.execute_query(query)
        if result:
            lines = result.split('\n')
            if len(lines) > 1:
                total = lines[1]
                print(f"📊 Tổng số brand_office: {total}")
        
        # Đếm records có address_old
        query = "SELECT COUNT(*) as with_address FROM brand_office WHERE address_old IS NOT NULL AND address_old != ''"
        result = self.execute_query(query)
        if result:
            lines = result.split('\n')
            if len(lines) > 1:
                with_address = lines[1]
                print(f"📍 Records có address_old: {with_address}")
        
        # Đếm records chưa có coordinates
        query = """
        SELECT COUNT(*) as need_geocoding 
        FROM brand_office 
        WHERE address_old IS NOT NULL 
        AND address_old != ''
        AND (latitude IS NULL OR longitude IS NULL OR latitude = 0 OR longitude = 0)
        """
        result = self.execute_query(query)
        if result:
            lines = result.split('\n')
            if len(lines) > 1:
                need_geocoding = lines[1]
                print(f"🎯 Records cần geocoding: {need_geocoding}")
        
        # Hiển thị 5 records mẫu
        print("\n📋 5 records mẫu cần geocoding:")
        query = """
        SELECT id, name, address_old, latitude, longitude
        FROM brand_office 
        WHERE address_old IS NOT NULL 
        AND address_old != ''
        AND (latitude IS NULL OR longitude IS NULL OR latitude = 0 OR longitude = 0)
        LIMIT 5
        """
        result = self.execute_query(query)
        if result:
            print(result)
    
    def run_test(self):
        """Chạy tất cả test"""
        print("🚀 BẮT ĐẦU TEST DATABASE VÀ BRAND_OFFICE")
        print("=" * 60)
        
        # Test kết nối
        if not self.test_connection():
            return False
        
        # Kiểm tra bảng
        if not self.check_brand_office_table():
            return False
        
        # Kiểm tra dữ liệu
        self.check_data_sample()
        
        print("\n" + "=" * 60)
        print("✅ HOÀN THÀNH TEST")
        print("💡 Bạn có thể chạy tool geocoding nếu mọi thứ OK")
        
        return True

def main():
    """Hàm main"""
    print("🔧 DATABASE & BRAND_OFFICE TESTER")
    print("=" * 60)
    
    tester = DatabaseTester()
    tester.run_test()

if __name__ == "__main__":
    main()
