-- T<PERSON><PERSON> bảng ward_geometry để lưu geometry data
-- Database: urbox

USE urbox;

-- T<PERSON><PERSON> bảng ward_geometry nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS `ward_geometry` (
  `id` int NOT NULL AUTO_INCREMENT,
  `pti_id` int DEFAULT NULL COMMENT 'ID từ maxa + 1',
  `data` json DEFAULT NULL COMMENT 'Geometry data từ API',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `pti_id` (`pti_id`),
  KEY `idx_pti_id` (`pti_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- <PERSON><PERSON><PERSON> thị cấu trúc bảng
DESCRIBE ward_geometry;
