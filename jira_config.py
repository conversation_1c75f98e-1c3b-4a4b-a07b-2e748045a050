#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Jira Configuration
Fill in your actual Jira credentials
"""

# Jira Configuration
JIRA_CONFIG = {
    # Jira instance URL
    'base_url': 'https://urbox.atlassian.net',

    # Your Jira email (the one you use to login)
    'email': '<EMAIL>',  # FILL THIS

    # Your Jira API token (create from: https://id.atlassian.com/manage-profile/security/api-tokens)
    'api_token': 'ATATT3xFfGF0W9N5Xovi_R3lZ1PZKOU21ZqCjIjle74xJui2tEeXpIFz-6q7cPRAC9I52rCww26E2dwmNlvPINe_fBwsqbMkoGhgMsJbLwxFpbOdaHBb-Lptk5M1BydFujufdv7T7fqi2SsWZicnizGMbQibBaRkac7nfW2C4nqTiqWLS6rrVY0=548B34C7',  # FILL THIS

    # Default assignee account ID (usually your own)
    'default_assignee': '70121:c41ffafb-2bc9-4512-86bf-f60f459f90b9',

    # Default parent epic for tasks
    'default_parent': 'UBG-2790'
}

def get_jira_config():
    """Get Jira configuration"""
    return JIRA_CONFIG

def validate_config():
    """Validate configuration"""
    required_fields = ['base_url', 'email', 'api_token', 'default_assignee']

    for field in required_fields:
        if not JIRA_CONFIG.get(field) or JIRA_CONFIG[field] in ['YOUR_ACTUAL_API_TOKEN_HERE', '<EMAIL>']:
            return False, f"Please configure {field} in jira_config.py"

    return True, "Configuration is valid"
