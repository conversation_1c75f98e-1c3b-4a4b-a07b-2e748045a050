#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import os
import json
from datetime import datetime

# Try to import config, fallback to template values
try:
    from jira_config import get_jira_config
    JIRA_CONFIG = get_jira_config()
except ImportError:
    print("⚠️ jira_config.py not found, using template values")
    print("Please copy jira_config_template.py to jira_config.py and configure it")
    JIRA_CONFIG = {
        'base_url': 'https://urbox.atlassian.net',
        'email': '<EMAIL>',
        'api_token': 'YOUR_ACTUAL_API_TOKEN_HERE',
        'default_assignee': '70121:c41ffafb-2bc9-4512-86bf-f60f459f90b9',
        'default_parent': 'UBG-2790'
    }

class JiraAutoUploader:
    def __init__(self):
        """
        Auto Jira uploader với config được fill sẵn
        """
        # Use configuration from config file
        self.base_url = JIRA_CONFIG['base_url']
        self.email = JIRA_CONFIG['email']
        self.api_token = JIRA_CONFIG['api_token']
        self.default_assignee = JIRA_CONFIG['default_assignee']
        
        # Session setup
        self.session = requests.Session()
        self.session.auth = (self.email, self.api_token)
        self.session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Atlassian-Token': 'no-check'
        })
    
    def create_task_and_upload(self, task_data, files_to_upload=None):
        """
        Tạo task và upload files trong một lần gọi
        
        Args:
            task_data (dict): Thông tin task
            files_to_upload (list): Danh sách file paths để upload
            
        Returns:
            dict: Kết quả tạo task và upload
        """
        result = {
            'task_created': False,
            'task_key': None,
            'files_uploaded': [],
            'errors': []
        }
        
        try:
            # 1. Tạo task trước
            print(f"🎯 Creating task: {task_data['summary']}")
            task_result = self.create_task(task_data)
            
            if not task_result:
                result['errors'].append("Failed to create task")
                return result
            
            result['task_created'] = True
            result['task_key'] = task_result['key']
            print(f"✅ Task created: {task_result['key']}")
            
            # 2. Upload files nếu có
            if files_to_upload:
                print(f"📁 Uploading {len(files_to_upload)} files...")
                upload_results = self.upload_files(task_result['key'], files_to_upload)
                result['files_uploaded'] = upload_results
            
            # 3. Chuyển trạng thái nếu cần
            if task_data.get('auto_transition'):
                self.transition_task(task_result['key'], task_data['auto_transition'])
            
            return result
            
        except Exception as e:
            result['errors'].append(f"Exception: {e}")
            return result
    
    def create_task(self, task_data):
        """Tạo Jira task"""
        url = f"{self.base_url}/rest/api/3/issue"
        
        # Build description content
        description_content = []
        
        # Main description
        if task_data.get('description'):
            description_content.append({
                "type": "paragraph",
                "content": [{"type": "text", "text": task_data['description']}]
            })
        
        # Tools/files section
        if task_data.get('tools'):
            description_content.append({
                "type": "paragraph", 
                "content": [{"type": "text", "text": "Tools đã tạo:", "marks": [{"type": "strong"}]}]
            })
            
            tool_items = []
            for tool in task_data['tools']:
                tool_items.append({
                    "type": "listItem",
                    "content": [{"type": "paragraph", "content": [{"type": "text", "text": tool}]}]
                })
            
            description_content.append({
                "type": "bulletList",
                "content": tool_items
            })
        
        # Technical details
        if task_data.get('technical_details'):
            description_content.append({
                "type": "paragraph",
                "content": [{"type": "text", "text": "Chi tiết kỹ thuật:", "marks": [{"type": "strong"}]}]
            })
            
            for detail in task_data['technical_details']:
                description_content.append({
                    "type": "paragraph",
                    "content": [{"type": "text", "text": f"• {detail}"}]
                })
        
        # Attribution
        description_content.extend([
            {"type": "rule"},
            {
                "type": "paragraph",
                "content": [
                    {"type": "text", "text": "Co-authored by"},
                    {
                        "type": "text", 
                        "text": " Augment Code",
                        "marks": [{
                            "type": "link",
                            "attrs": {"href": "https://www.augmentcode.com/?utm_source=atlassian&utm_medium=jira_issue&utm_campaign=jira"}
                        }]
                    }
                ]
            }
        ])
        
        # Build payload
        payload = {
            "fields": {
                "project": {"key": "UBG"},
                "summary": task_data['summary'],
                "description": {
                    "type": "doc",
                    "version": 1,
                    "content": description_content
                },
                "issuetype": {"name": task_data.get('issue_type', 'Task')},
                "assignee": {"accountId": self.default_assignee},
                "priority": {"name": task_data.get('priority', 'Medium')},
                "labels": task_data.get('labels', [])
            }
        }
        
        # Add parent if specified
        if task_data.get('parent_key'):
            payload['fields']['parent'] = {"key": task_data['parent_key']}
        
        try:
            response = self.session.post(url, json=payload)
            
            if response.status_code == 201:
                return response.json()
            else:
                print(f"❌ Failed to create task: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Exception creating task: {e}")
            return None
    
    def upload_files(self, issue_key, file_paths):
        """Upload multiple files to issue"""
        results = []
        
        for file_path in file_paths:
            if not os.path.exists(file_path):
                results.append({
                    'file': file_path,
                    'success': False,
                    'error': 'File not found'
                })
                continue
            
            result = self.upload_single_file(issue_key, file_path)
            results.append(result)
        
        return results
    
    def upload_single_file(self, issue_key, file_path):
        """Upload single file"""
        url = f"{self.base_url}/rest/api/3/issue/{issue_key}/attachments"
        
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        print(f"  📎 Uploading: {file_name} ({file_size:,} bytes)")
        
        try:
            # Remove Content-Type for file upload
            headers = {'X-Atlassian-Token': 'no-check'}
            
            with open(file_path, 'rb') as file:
                files = {'file': (file_name, file, 'application/octet-stream')}
                
                response = requests.post(
                    url, 
                    files=files, 
                    headers=headers,
                    auth=(self.email, self.api_token)
                )
                
                if response.status_code == 200:
                    attachment_data = response.json()[0]
                    print(f"    ✅ Success - ID: {attachment_data['id']}")
                    return {
                        'file': file_path,
                        'success': True,
                        'attachment_id': attachment_data['id'],
                        'download_url': attachment_data['content']
                    }
                else:
                    print(f"    ❌ Failed: {response.status_code}")
                    return {
                        'file': file_path,
                        'success': False,
                        'error': f"HTTP {response.status_code}: {response.text}"
                    }
                    
        except Exception as e:
            print(f"    ❌ Exception: {e}")
            return {
                'file': file_path,
                'success': False,
                'error': str(e)
            }
    
    def transition_task(self, issue_key, target_status):
        """Chuyển trạng thái task"""
        # Get available transitions
        url = f"{self.base_url}/rest/api/3/issue/{issue_key}/transitions"
        
        try:
            response = self.session.get(url)
            if response.status_code != 200:
                print(f"❌ Failed to get transitions: {response.status_code}")
                return False
            
            transitions = response.json()['transitions']
            
            # Find transition by name
            transition_id = None
            for transition in transitions:
                if transition['name'].lower() == target_status.lower():
                    transition_id = transition['id']
                    break
                elif transition['to']['name'].lower() == target_status.lower():
                    transition_id = transition['id']
                    break
            
            if not transition_id:
                print(f"❌ Transition '{target_status}' not found")
                return False
            
            # Execute transition
            payload = {"transition": {"id": transition_id}}
            response = self.session.post(url, json=payload)
            
            if response.status_code == 204:
                print(f"✅ Transitioned to: {target_status}")
                return True
            else:
                print(f"❌ Failed to transition: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Exception during transition: {e}")
            return False

# Predefined task templates
TASK_TEMPLATES = {
    'geometry_crawl': {
        'summary': 'Crawl geometry data cho xã phường',
        'description': 'Crawl dữ liệu geometry (tọa độ đa giác) cho tất cả xã phường từ API sapnhap.bando.com.vn và lưu vào bảng ward_geometry.',
        'parent_key': 'UBG-2790',
        'labels': ['UrGift', 'geometry', 'crawl', 'ward'],
        'tools': [
            'crawl_geometry.py - Tool chính crawl geometry cho tất cả xã',
            'crawl_single_geometry.py - Tool crawl geometry cho 1 xã cụ thể',
            'crawl_missing_geometry.py - Tool crawl các xã còn thiếu',
            'create_ward_geometry_table.sql - Script tạo bảng'
        ],
        'technical_details': [
            'API: https://sapnhap.bando.com.vn/pread_json',
            'Format ID: xa3321.{maxa+1}',
            'Output: JSON geometry data (GeoJSON format)',
            'Đã crawl: 3,312/3,321 records (còn thiếu 9 records có maxa=-1)'
        ],
        'auto_transition': 'In Progress'
    },
    
    'brand_office_coordinates': {
        'summary': 'Tạo tool cập nhật coordinates cho brand_office',
        'description': 'Đã tạo tool sử dụng Google Maps API để lấy latitude, longitude từ address_old và cập nhật vào bảng brand_office.',
        'parent_key': 'UBG-2790',
        'labels': ['UrGift', 'coordinates', 'geocoding', 'brand_office'],
        'tools': [
            'update_brand_office_coordinates.py - Tool chính với đầy đủ tính năng',
            'test_brand_office_db.py - Test database connection và kiểm tra dữ liệu',
            'GOOGLE_MAPS_API_SETUP.md - Hướng dẫn cấu hình Google Maps API',
            'README_BRAND_OFFICE_GEOCODING.md - Hướng dẫn sử dụng chi tiết'
        ],
        'technical_details': [
            'Google Maps Geocoding API integration',
            'Rate limiting và error handling',
            'Dry run mode để test trước khi cập nhật',
            'CSV export với báo cáo chi tiết',
            'Database direct update capability'
        ],
        'auto_transition': 'Done'
    }
}

def quick_upload(template_name, files=None, custom_data=None):
    """
    Quick function để upload với template có sẵn
    
    Args:
        template_name (str): Tên template ('geometry_crawl', 'brand_office_coordinates')
        files (list): Danh sách file paths
        custom_data (dict): Override data cho template
    """
    uploader = JiraAutoUploader()
    
    if template_name not in TASK_TEMPLATES:
        print(f"❌ Template '{template_name}' not found!")
        print(f"Available templates: {list(TASK_TEMPLATES.keys())}")
        return None
    
    # Get template data
    task_data = TASK_TEMPLATES[template_name].copy()
    
    # Override with custom data if provided
    if custom_data:
        task_data.update(custom_data)
    
    # Execute
    print(f"🚀 Auto-creating task with template: {template_name}")
    result = uploader.create_task_and_upload(task_data, files)
    
    # Summary
    if result['task_created']:
        print(f"\n🎉 SUCCESS!")
        print(f"📋 Task: {result['task_key']}")
        print(f"🔗 URL: https://urbox.atlassian.net/browse/{result['task_key']}")
        
        if result['files_uploaded']:
            successful = sum(1 for f in result['files_uploaded'] if f['success'])
            print(f"📎 Files uploaded: {successful}/{len(result['files_uploaded'])}")
    else:
        print(f"\n❌ FAILED!")
        for error in result['errors']:
            print(f"Error: {error}")
    
    return result

def main():
    """Demo usage"""
    print("🔧 JIRA AUTO UPLOADER DEMO")
    print("=" * 60)
    
    # Example 1: Geometry crawl với files
    geometry_files = [
        "crawl_geometry.py",
        "crawl_single_geometry.py", 
        "crawl_missing_geometry.py",
        "create_ward_geometry_table.sql"
    ]
    
    print("Example 1: Geometry crawl task")
    # result = quick_upload('geometry_crawl', geometry_files)
    
    # Example 2: Brand office coordinates
    brand_office_files = [
        "update_brand_office_coordinates.py",
        "test_brand_office_db.py",
        "GOOGLE_MAPS_API_SETUP.md",
        "README_BRAND_OFFICE_GEOCODING.md"
    ]
    
    print("\nExample 2: Brand office coordinates task")
    # result = quick_upload('brand_office_coordinates', brand_office_files)

if __name__ == "__main__":
    main()
