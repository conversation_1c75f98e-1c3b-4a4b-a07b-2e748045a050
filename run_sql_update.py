#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script chạy file SQL update vào MySQL (dùng cho update_brand_office_latlong.sql)
"""
import subprocess
import sys

DB_HOST = "localhost"
DB_PORT = 3306
DB_USER = "root"
DB_PASS = "root"
DB_NAME = "urbox"
SQL_FILE = "update_brand_office_latlong.sql"

def run_sql_file():
    print(f"Đang chạy file SQL: {SQL_FILE}")
    cmd = [
        "mysql", "-u", DB_USER, f"-p{DB_PASS}", "-h", DB_HOST, "-P", str(DB_PORT),
        "-D", DB_NAME, "<", SQL_FILE
    ]
    # subprocess không hỗ trợ redirect '<', nên dùng shell=True
    cmd_str = f"mysql -u {DB_USER} -p{DB_PASS} -h {DB_HOST} -P {DB_PORT} -D {DB_NAME} < {SQL_FILE}"
    result = subprocess.run(cmd_str, shell=True)
    if result.returncode == 0:
        print("✅ Đã cập nhật thành công!")
    else:
        print("❌ Có lỗi khi chạy file SQL!")

if __name__ == "__main__":
    run_sql_file() 