# Tool Crawl Dữ Liệu Xã Phường API

## Mô tả
Tool này được tạo để crawl dữ liệu xã phường từ API `https://sapnhap.bando.com.vn/ptracuu` và lưu vào bảng `xaphuong` trong database MySQL.

## Cấu trúc Files

### 1. `crawl_xaphuong_api.py`
- **M<PERSON><PERSON> đích**: Tool cơ bản để crawl dữ liệu một tỉnh thành
- **Class chính**: `XaPhuongCrawler`
- **Chức năng**:
  - Gọi API để lấy dữ liệu xã phường
  - Transform dữ liệu từ format API sang format database
  - Lưu dữ liệu vào bảng `xaphuong`
  - Tạo file backup SQL

### 2. `crawl_all_provinces.py`
- **Mục đích**: Tool nâng cao để crawl nhiều tỉnh thành
- **Chức năng**:
  - Crawl tất cả tỉnh thành
  - Crawl một tỉnh cụ thể
  - Crawl nhiều tỉnh cụ thể
  - Xem danh sách tỉnh thành

## Cách sử dụng

### Tool cơ bản
```bash
# Chạy tool cơ bản (mặc định crawl tỉnh Lào Cai - ID 13)
python3 crawl_xaphuong_api.py
```

### Tool nâng cao
```bash
# Xem hướng dẫn
python3 crawl_all_provinces.py

# Xem danh sách tỉnh thành
python3 crawl_all_provinces.py list

# Crawl một tỉnh cụ thể (ví dụ: Hà Nội - ID 1)
python3 crawl_all_provinces.py single 1

# Crawl nhiều tỉnh cụ thể
python3 crawl_all_provinces.py multiple 1,13,29

# Crawl tất cả tỉnh thành (cẩn thận!)
python3 crawl_all_provinces.py all
```

## Cấu trúc dữ liệu

### API Response Format
```json
{
    "id": 408,
    "matinh": 13,
    "ma": "0408",
    "tentinh": "tỉnh Lào Cai",
    "loai": "xã",
    "tenhc": "Yên Bình",
    "cay": "0310.0408",
    "dientichkm2": 9,
    "dansonguoi": "653422",
    "trungtamhc": "đang cập nhật",
    "kinhdo": 104.393,
    "vido": 22.036,
    "truocsapnhap": "Thị trấn Yên Bình, Xã Tân Hương...",
    "maxa": -1
}
```

### Database Mapping
| API Field | Database Field | Mô tả |
|-----------|----------------|-------|
| `matinh` | `matinh` | ID tỉnh thành |
| `ma` | `ma` | Mã xã phường |
| `tentinh` | `tentinh` | Tên tỉnh thành |
| `loai` | `loai` | Loại: xã, phường, thị trấn |
| `tenhc` | `tenhc` | Tên hành chính |
| `cay` | `cay` | Mã cây |
| `dientichkm2` | `dientichkm2` | Diện tích km2 |
| `dansonguoi` | `dansonguoi` | Dân số người |
| `trungtamhc` | `trungtamhc` | Trung tâm hành chính |
| `kinhdo` | `kinhdo` | Kinh độ |
| `vido` | `vido` | Vĩ độ |
| `truocsapnhap` | `truocsapnhap` | Trước sáp nhập |
| `maxa` | `maxa` | Mã xã |

## Cấu hình Database

### Thông tin kết nối
- **Host**: localhost
- **Port**: 3306
- **User**: root
- **Password**: root
- **Database**: urbox

### Bảng nguồn dữ liệu
- **Bảng tỉnh thành**: `tinhthanh`
- **Field ID**: `mahc` (Mã hành chính)
- **Field tên**: `tentinh`

### Bảng `xaphuong`
```sql
CREATE TABLE `xaphuong` (
  `id` int NOT NULL AUTO_INCREMENT,
  `matinh` int NOT NULL,
  `ma` varchar(10) NOT NULL,
  `tentinh` varchar(255) DEFAULT NULL,
  `loai` varchar(50) DEFAULT NULL,
  `tenhc` varchar(255) NOT NULL,
  `cay` varchar(50) DEFAULT NULL,
  `dientichkm2` int DEFAULT NULL,
  `dansonguoi` varchar(50) DEFAULT NULL,
  `trungtamhc` varchar(255) DEFAULT NULL,
  `kinhdo` decimal(10,6) DEFAULT NULL,
  `vido` decimal(10,6) DEFAULT NULL,
  `truocsapnhap` text DEFAULT NULL,
  `maxa` int DEFAULT NULL,
  `geo_data` longtext DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

## Kiểm tra dữ liệu

### Kiểm tra tổng số records
```sql
SELECT COUNT(*) as total FROM xaphuong;
```

### Kiểm tra theo tỉnh
```sql
SELECT matinh, COUNT(*) as total
FROM xaphuong
GROUP BY matinh
ORDER BY matinh;
```

### Kiểm tra dữ liệu mẫu
```sql
-- Kiểm tra dữ liệu Hà Nội (mahc = 1)
SELECT id, matinh, ma, tentinh, loai, tenhc
FROM xaphuong
WHERE matinh = 1
LIMIT 5;

-- Kiểm tra dữ liệu Điện Biên (mahc = 13)
SELECT id, matinh, ma, tentinh, loai, tenhc
FROM xaphuong
WHERE matinh = 13
LIMIT 5;
```

## Lưu ý quan trọng

### 1. Backup tự động
- Tool tự động tạo file backup SQL với format: `crawl_xaphuong_backup_YYYYMMDD_HHMMSS.sql`
- File backup chứa tất cả câu lệnh INSERT để có thể khôi phục nếu cần

### 2. Rate limiting
- Tool có delay 2 giây giữa các request để tránh spam API
- Không nên crawl quá nhiều tỉnh cùng lúc

### 3. Error handling
- Tool có xử lý lỗi và báo cáo chi tiết
- Nếu một tỉnh thất bại, tool vẫn tiếp tục với tỉnh khác

### 4. Dữ liệu trùng lặp
- Tool không kiểm tra trùng lặp, có thể tạo duplicate records
- Nên kiểm tra và xóa dữ liệu cũ trước khi crawl lại

## Troubleshooting

### Lỗi kết nối database
```bash
# Kiểm tra MySQL service
brew services list | grep mysql

# Khởi động MySQL nếu cần
brew services start mysql
```

### Lỗi API timeout
- Kiểm tra kết nối internet
- Thử lại với tỉnh khác
- Tăng timeout trong code nếu cần

### Lỗi encoding
- Tool đã xử lý UTF-8 encoding
- Nếu vẫn lỗi, kiểm tra charset của database

## Ví dụ sử dụng thực tế

### Test với một tỉnh
```bash
# Test với Hà Nội (mahc = 1)
python3 crawl_all_provinces.py single 1
```

### Crawl một số tỉnh lớn
```bash
# Crawl Hà Nội, TP.HCM, Đà Nẵng (mahc = 1, 29, 21)
python3 crawl_all_provinces.py multiple 1,29,21
```

### Kiểm tra kết quả
```sql
-- Xem tổng số xã phường đã crawl
SELECT
    t.tentinh as province_name,
    COUNT(x.id) as ward_count
FROM xaphuong x
JOIN tinhthanh t ON x.matinh = t.mahc
GROUP BY x.matinh, t.tentinh
ORDER BY ward_count DESC;
```
