# Manual Upload Instructions for UBG-2816

## 📋 Files cần upload cho task UBG-2816:

### ✅ Files đã sẵn sàng:
1. **crawl_geometry.py** - Tool chính crawl geometry cho tất cả xã
2. **crawl_single_geometry.py** - Tool crawl geometry cho 1 xã cụ thể  
3. **crawl_missing_geometry.py** - Tool crawl các xã còn thiếu
4. **create_ward_geometry_table.sql** - Script tạo bảng ward_geometry
5. **sample_geometry.json** - Dữ liệu geometry mẫu

### 🔧 Upload tools đã tạo:
- **jira_integration.py** - <PERSON><PERSON> tích hợp upload tự động
- **upload_to_ubg2816.py** - Script upload trực tiếp
- **upload_files.sh** - Bash script upload
- **test_jira_upload.py** - Test suite

## 🚀 Cách upload (chọn 1 trong các cách):

### Cách 1: Sử dụng Python tool
```bash
python3 upload_to_ubg2816.py
```

### Cách 2: Sử dụng bash script
```bash
chmod +x upload_files.sh
./upload_files.sh
```

### Cách 3: Sử dụng curl commands
```bash
# Upload từng file
curl -X POST \
  -H "X-Atlassian-Token: no-check" \
  -F "file=@crawl_geometry.py" \
  -u "<EMAIL>:ATATT3xFfGF0W9N5Xovi_R3lZ1PZKOU21ZqCjIjle74xJui2tEeXpIFz-6q7cPRAC9I52rCww26E2dwmNlvPINe_fBwsqbMkoGhgMsJbLwxFpbOdaHBb-Lptk5M1BydFujufdv7T7fqi2SsWZicnizGMbQibBaRkac7nfW2C4nqTiqWLS6rrVY0=548B34C7" \
  "https://urbox.atlassian.net/rest/api/3/issue/UBG-2816/attachments"

curl -X POST \
  -H "X-Atlassian-Token: no-check" \
  -F "file=@crawl_single_geometry.py" \
  -u "<EMAIL>:ATATT3xFfGF0W9N5Xovi_R3lZ1PZKOU21ZqCjIjle74xJui2tEeXpIFz-6q7cPRAC9I52rCww26E2dwmNlvPINe_fBwsqbMkoGhgMsJbLwxFpbOdaHBb-Lptk5M1BydFujufdv7T7fqi2SsWZicnizGMbQibBaRkac7nfW2C4nqTiqWLS6rrVY0=548B34C7" \
  "https://urbox.atlassian.net/rest/api/3/issue/UBG-2816/attachments"

curl -X POST \
  -H "X-Atlassian-Token: no-check" \
  -F "file=@crawl_missing_geometry.py" \
  -u "<EMAIL>:ATATT3xFfGF0W9N5Xovi_R3lZ1PZKOU21ZqCjIjle74xJui2tEeXpIFz-6q7cPRAC9I52rCww26E2dwmNlvPINe_fBwsqbMkoGhgMsJbLwxFpbOdaHBb-Lptk5M1BydFujufdv7T7fqi2SsWZicnizGMbQibBaRkac7nfW2C4nqTiqWLS6rrVY0=548B34C7" \
  "https://urbox.atlassian.net/rest/api/3/issue/UBG-2816/attachments"

curl -X POST \
  -H "X-Atlassian-Token: no-check" \
  -F "file=@create_ward_geometry_table.sql" \
  -u "<EMAIL>:ATATT3xFfGF0W9N5Xovi_R3lZ1PZKOU21ZqCjIjle74xJui2tEeXpIFz-6q7cPRAC9I52rCww26E2dwmNlvPINe_fBwsqbMkoGhgMsJbLwxFpbOdaHBb-Lptk5M1BydFujufdv7T7fqi2SsWZicnizGMbQibBaRkac7nfW2C4nqTiqWLS6rrVY0=548B34C7" \
  "https://urbox.atlassian.net/rest/api/3/issue/UBG-2816/attachments"

curl -X POST \
  -H "X-Atlassian-Token: no-check" \
  -F "file=@sample_geometry.json" \
  -u "<EMAIL>:ATATT3xFfGF0W9N5Xovi_R3lZ1PZKOU21ZqCjIjle74xJui2tEeXpIFz-6q7cPRAC9I52rCww26E2dwmNlvPINe_fBwsqbMkoGhgMsJbLwxFpbOdaHBb-Lptk5M1BydFujufdv7T7fqi2SsWZicnizGMbQibBaRkac7nfW2C4nqTiqWLS6rrVY0=548B34C7" \
  "https://urbox.atlassian.net/rest/api/3/issue/UBG-2816/attachments"
```

### Cách 4: Upload thủ công qua Jira Web UI
1. Truy cập: https://urbox.atlassian.net/browse/UBG-2816
2. Click vào "Attach" hoặc "📎" icon
3. Chọn và upload từng file:
   - crawl_geometry.py
   - crawl_single_geometry.py
   - crawl_missing_geometry.py
   - create_ward_geometry_table.sql
   - sample_geometry.json

## 📊 Expected Results:

Sau khi upload thành công, bạn sẽ thấy:
- ✅ 5 files được attach vào task UBG-2816
- 📎 Files có thể download từ Jira
- 🔗 Links download trong response JSON

## 🔍 Verify Upload:

Kiểm tra upload thành công bằng cách:
1. Truy cập task: https://urbox.atlassian.net/browse/UBG-2816
2. Scroll xuống phần "Attachments"
3. Verify 5 files đã được upload

## ⚠️ Troubleshooting:

### Nếu gặp lỗi 401 Unauthorized:
- Kiểm tra email và API token
- Verify API token chưa expire

### Nếu gặp lỗi 403 Forbidden:
- Kiểm tra quyền upload file vào issue
- Verify bạn có quyền edit task UBG-2816

### Nếu gặp lỗi 404 Not Found:
- Kiểm tra issue key UBG-2816 có tồn tại
- Verify URL Jira instance đúng

### Nếu gặp lỗi 413 Request Entity Too Large:
- File quá lớn (>10MB)
- Nén file hoặc chia nhỏ

## 📞 Support:

Nếu cần hỗ trợ:
1. Check console output để xem error message
2. Verify network connection
3. Test với file nhỏ trước
4. Liên hệ Jira admin nếu cần cấp quyền
