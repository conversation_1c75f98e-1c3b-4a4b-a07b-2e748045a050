# Changelog - Tool Crawl Xã Phường API

## Version 2.0 - 2025-07-11

### 🎉 Major Changes

#### Thay đổi bảng lưu trữ
- **TRƯỚC**: <PERSON><PERSON><PERSON> vào bảng `ward` 
- **SAU**: <PERSON><PERSON><PERSON> vào bảng `xaphuong`

#### Thay đổi nguồn dữ liệu tỉnh thành
- **TRƯỚC**: <PERSON><PERSON><PERSON> từ bảng `___province` với field `id`
- **SAU**: <PERSON><PERSON><PERSON> từ bảng `tinhthanh` với field `mahc`

### 🔄 Database Schema Changes

#### Bảng `xaphuong` (mới)
```sql
CREATE TABLE `xaphuong` (
  `id` int NOT NULL AUTO_INCREMENT,
  `matinh` int NOT NULL,
  `ma` varchar(10) NOT NULL,
  `tentinh` varchar(255) DEFAULT NULL,
  `loai` varchar(50) DEFAULT NULL,
  `tenhc` varchar(255) NOT NULL,
  `cay` varchar(50) DEFAULT NULL,
  `dientichkm2` int DEFAULT NULL,
  `dansonguoi` varchar(50) DEFAULT NULL,
  `trungtamhc` varchar(255) DEFAULT NULL,
  `kinhdo` decimal(10,6) DEFAULT NULL,
  `vido` decimal(10,6) DEFAULT NULL,
  `truocsapnhap` text DEFAULT NULL,
  `maxa` int DEFAULT NULL,
  `geo_data` longtext DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 📊 Data Mapping Changes

#### Mapping mới (API → Database)
| API Field | Database Field | Mô tả |
|-----------|----------------|-------|
| `matinh` | `matinh` | ID tỉnh thành |
| `ma` | `ma` | Mã xã phường |
| `tentinh` | `tentinh` | Tên tỉnh thành |
| `loai` | `loai` | Loại: xã, phường, thị trấn |
| `tenhc` | `tenhc` | Tên hành chính |
| `cay` | `cay` | Mã cây |
| `dientichkm2` | `dientichkm2` | Diện tích km2 |
| `dansonguoi` | `dansonguoi` | Dân số người |
| `trungtamhc` | `trungtamhc` | Trung tâm hành chính |
| `kinhdo` | `kinhdo` | Kinh độ |
| `vido` | `vido` | Vĩ độ |
| `truocsapnhap` | `truocsapnhap` | Trước sáp nhập |
| `maxa` | `maxa` | Mã xã |

### 🆔 Province ID Changes

#### Ví dụ thay đổi ID
| Tỉnh thành | ID cũ (___province) | ID mới (tinhthanh.mahc) |
|------------|---------------------|-------------------------|
| Hà Nội | 82 | 1 |
| TP.HCM | 109 | 29 |
| Đà Nẵng | 102 | 21 |
| Điện Biên | - | 13 |

### 📁 File Changes

#### Files được cập nhật
- `crawl_xaphuong_api.py`: Cập nhật để lưu vào bảng `xaphuong`
- `crawl_all_provinces.py`: Cập nhật để lấy dữ liệu từ bảng `tinhthanh`
- `README_crawl_tool.md`: Cập nhật documentation
- `province_id_mapping.md`: Cập nhật mapping ID

#### Files mới
- `CHANGELOG.md`: File này

### 🔧 Command Changes

#### Lệnh kiểm tra dữ liệu mới
```sql
-- Kiểm tra tổng số records
SELECT COUNT(*) as total FROM xaphuong;

-- Kiểm tra theo tỉnh
SELECT matinh, COUNT(*) as total FROM xaphuong GROUP BY matinh ORDER BY matinh;

-- Kiểm tra dữ liệu với join
SELECT x.matinh, t.tentinh, COUNT(x.id) as ward_count 
FROM xaphuong x 
JOIN tinhthanh t ON x.matinh = t.mahc 
GROUP BY x.matinh, t.tentinh 
ORDER BY x.matinh;
```

### 📝 Backup File Changes
- **TRƯỚC**: `crawl_ward_backup_YYYYMMDD_HHMMSS.sql`
- **SAU**: `crawl_xaphuong_backup_YYYYMMDD_HHMMSS.sql`

### ✅ Test Results

#### Crawl thành công
- **Hà Nội (ID: 1)**: 126 xã phường
- **Điện Biên (ID: 13)**: 92 xã phường (có duplicate từ test trước)

#### Dữ liệu lưu trữ
```
+--------+----------------------+------------+
| matinh | tentinh              | ward_count |
+--------+----------------------+------------+
|      1 | Thủ đô Hà Nội        |        126 |
|     13 | tỉnh Điện Biên       |         92 |
+--------+----------------------+------------+
```

### 🚀 Usage Examples

#### Crawl commands mới
```bash
# Xem danh sách tỉnh thành
python3 crawl_all_provinces.py list

# Crawl Hà Nội (ID mới: 1)
python3 crawl_all_provinces.py single 1

# Crawl các thành phố lớn
python3 crawl_all_provinces.py multiple 1,29,21,4,33
```

### 🔍 Migration Notes

#### Nếu cần migrate dữ liệu cũ
```sql
-- Backup dữ liệu cũ từ bảng ward
CREATE TABLE ward_backup AS SELECT * FROM ward WHERE is_merge = 2;

-- Xóa dữ liệu cũ nếu cần
-- DELETE FROM ward WHERE is_merge = 2;
```

### 🎯 Benefits

1. **Mapping hoàn hảo**: Dữ liệu API khớp 100% với cấu trúc bảng `xaphuong`
2. **Không mất dữ liệu**: Lưu trữ đầy đủ tất cả thông tin từ API
3. **Dễ truy vấn**: Cấu trúc đơn giản, dễ hiểu
4. **Timestamp tự động**: `created_at` và `updated_at` tự động
5. **ID nhất quán**: Sử dụng `mahc` từ bảng `tinhthanh` chính thức

### 🔮 Future Enhancements

1. Thêm validation dữ liệu trước khi insert
2. Thêm tính năng update thay vì chỉ insert
3. Thêm logging chi tiết hơn
4. Thêm tính năng crawl incremental
5. Thêm API endpoint để check status crawl
