#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv

def compare_province_data():
    """So sánh dữ liệu giữa province.csv và database ___province"""
    
    print("🔍 So sánh dữ liệu province.csv với database...")
    
    # Đọc dữ liệu từ CSV
    csv_data = {}
    csv_ids = set()
    csv_pti_ids = set()
    
    try:
        with open('province.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row['id'].strip():  # Bỏ qua dòng trống
                    db_id = int(row['id'])
                    pti_id = int(row['province_pti_id'])
                    title = row['province_title'].strip()
                    
                    csv_data[db_id] = {
                        'pti_id': pti_id,
                        'title': title
                    }
                    csv_ids.add(db_id)
                    csv_pti_ids.add(pti_id)
        
        print(f"📊 CSV có {len(csv_data)} records")
        print(f"📋 ID range trong CSV: {min(csv_ids)} - {max(csv_ids)}")
        print(f"📋 PTI ID range trong CSV: {min(csv_pti_ids)} - {max(csv_pti_ids)}")
        
        # Tạo SQL query để kiểm tra
        id_list = ','.join(map(str, csv_ids))
        
        print(f"\n🔍 Các ID trong CSV: {sorted(csv_ids)}")
        print(f"\n🔍 Các PTI ID trong CSV: {sorted(csv_pti_ids)}")
        
        # Tạo file SQL để kiểm tra
        sql_queries = [
            f"-- Kiểm tra các ID từ CSV có tồn tại trong database",
            f"SELECT id, pti_id, title, is_city, is_megre FROM ___province WHERE id IN ({id_list}) ORDER BY id;",
            f"",
            f"-- Kiểm tra các PTI ID từ CSV",
            f"SELECT id, pti_id, title FROM ___province WHERE pti_id IN ({','.join(map(str, csv_pti_ids))}) ORDER BY pti_id;",
            f"",
            f"-- Tìm các records trong database KHÔNG có trong CSV",
            f"SELECT id, pti_id, title, is_megre FROM ___province WHERE id NOT IN ({id_list}) ORDER BY id;",
        ]
        
        with open('check_province_queries.sql', 'w', encoding='utf-8') as f:
            f.write('\n'.join(sql_queries))
        
        print(f"\n💾 Đã tạo file check_province_queries.sql để kiểm tra")
        
        # In ra một vài thông tin so sánh
        print(f"\n📋 Một vài records từ CSV:")
        for i, (db_id, data) in enumerate(list(csv_data.items())[:5]):
            print(f"   ID {db_id}: PTI {data['pti_id']} - {data['title']}")
        
        return csv_data
        
    except Exception as e:
        print(f"❌ Lỗi khi đọc CSV: {e}")
        return {}

if __name__ == "__main__":
    compare_province_data()
