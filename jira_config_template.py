#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Jira Configuration Template
Copy this file to jira_config.py and fill in your actual credentials
"""

# Jira Configuration
JIRA_CONFIG = {
    # Jira instance URL
    'base_url': 'https://urbox.atlassian.net',
    
    # Your Jira email (the one you use to login)
    'email': '<EMAIL>',
    
    # Your Jira API token (create from: https://id.atlassian.com/manage-profile/security/api-tokens)
    'api_token': 'YOUR_ACTUAL_API_TOKEN_HERE',
    
    # Default assignee account ID (usually your own)
    'default_assignee': '70121:c41ffafb-2bc9-4512-86bf-f60f459f90b9',
    
    # Default parent epic for tasks
    'default_parent': 'UBG-2790'
}

# Project Configuration
PROJECT_CONFIG = {
    'key': 'UBG',
    'name': 'UrGift'
}

# Default Labels
DEFAULT_LABELS = ['UrGift']

# Task Templates Configuration
TASK_TEMPLATES_CONFIG = {
    'default_priority': 'Medium',
    'default_issue_type': 'Task',
    'auto_transition_to_done': True,
    'add_augment_attribution': True
}

def get_jira_config():
    """Get Jira configuration"""
    return JIRA_CONFIG

def get_project_config():
    """Get project configuration"""
    return PROJECT_CONFIG

def validate_config():
    """Validate configuration"""
    required_fields = ['base_url', 'email', 'api_token', 'default_assignee']
    
    for field in required_fields:
        if not JIRA_CONFIG.get(field) or JIRA_CONFIG[field] in ['YOUR_ACTUAL_API_TOKEN_HERE', '<EMAIL>']:
            return False, f"Please configure {field} in jira_config.py"
    
    return True, "Configuration is valid"

if __name__ == "__main__":
    is_valid, message = validate_config()
    if is_valid:
        print("✅ Configuration is valid")
    else:
        print(f"❌ {message}")
        print("\nPlease:")
        print("1. Copy this file to jira_config.py")
        print("2. Fill in your actual Jira credentials")
        print("3. Run the script again")
