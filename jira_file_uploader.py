#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import os
import sys
import json
from pathlib import Path

class JiraFileUploader:
    def __init__(self, base_url, email, api_token):
        """
        Initialize Jira File Uploader
        
        Args:
            base_url (str): Jira base URL (e.g., 'https://yourcompany.atlassian.net')
            email (str): Your Jira email
            api_token (str): Your Jira API token
        """
        self.base_url = base_url.rstrip('/')
        self.email = email
        self.api_token = api_token
        self.session = requests.Session()
        self.session.auth = (email, api_token)
        self.session.headers.update({
            'Accept': 'application/json',
            'X-Atlassian-Token': 'no-check'
        })
    
    def upload_file(self, issue_key, file_path):
        """
        Upload a file to a Jira issue
        
        Args:
            issue_key (str): Jira issue key (e.g., 'UBG-2816')
            file_path (str): Path to the file to upload
            
        Returns:
            dict: Response from Jira API
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        print(f"📁 Uploading file: {file_name}")
        print(f"📊 File size: {file_size:,} bytes")
        print(f"🎯 Target issue: {issue_key}")
        
        url = f"{self.base_url}/rest/api/3/issue/{issue_key}/attachments"
        
        try:
            with open(file_path, 'rb') as file:
                files = {
                    'file': (file_name, file, 'application/octet-stream')
                }
                
                response = self.session.post(url, files=files)
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Upload successful!")
                    print(f"📎 Attachment ID: {result[0]['id']}")
                    print(f"🔗 Download URL: {result[0]['content']}")
                    return result
                else:
                    print(f"❌ Upload failed!")
                    print(f"Status Code: {response.status_code}")
                    print(f"Response: {response.text}")
                    return None
                    
        except Exception as e:
            print(f"❌ Exception during upload: {e}")
            return None
    
    def upload_multiple_files(self, issue_key, file_paths):
        """
        Upload multiple files to a Jira issue
        
        Args:
            issue_key (str): Jira issue key
            file_paths (list): List of file paths to upload
            
        Returns:
            list: List of upload results
        """
        results = []
        
        print(f"🚀 Starting batch upload to {issue_key}")
        print(f"📋 Files to upload: {len(file_paths)}")
        print()
        
        for i, file_path in enumerate(file_paths, 1):
            print(f"[{i}/{len(file_paths)}] Processing: {os.path.basename(file_path)}")
            result = self.upload_file(issue_key, file_path)
            results.append({
                'file_path': file_path,
                'success': result is not None,
                'result': result
            })
            print()
        
        # Summary
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful
        
        print("=" * 60)
        print("📊 UPLOAD SUMMARY")
        print("=" * 60)
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📋 Total: {len(results)}")
        
        return results
    
    def get_issue_attachments(self, issue_key):
        """
        Get list of attachments for an issue
        
        Args:
            issue_key (str): Jira issue key
            
        Returns:
            list: List of attachments
        """
        url = f"{self.base_url}/rest/api/3/issue/{issue_key}"
        params = {'fields': 'attachment'}
        
        try:
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                attachments = data['fields']['attachment']
                
                print(f"📎 Attachments for {issue_key}:")
                for att in attachments:
                    print(f"  - {att['filename']} ({att['size']:,} bytes)")
                
                return attachments
            else:
                print(f"❌ Failed to get attachments: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            return []

def main():
    """Main function with interactive menu"""
    print("🔧 JIRA FILE UPLOADER TOOL")
    print("=" * 60)
    
    # Configuration
    print("📋 Configuration needed:")
    print("1. Jira Base URL (e.g., https://yourcompany.atlassian.net)")
    print("2. Your Jira email")
    print("3. Your Jira API token")
    print()
    
    # Get configuration
    base_url = input("Enter Jira Base URL: ").strip()
    if not base_url:
        print("❌ Base URL is required!")
        return
    
    email = input("Enter your Jira email: ").strip()
    if not email:
        print("❌ Email is required!")
        return
    
    api_token = input("Enter your Jira API token: ").strip()
    if not api_token:
        print("❌ API token is required!")
        return
    
    # Initialize uploader
    uploader = JiraFileUploader(base_url, email, api_token)
    
    while True:
        print("\n" + "=" * 60)
        print("📋 MENU OPTIONS")
        print("=" * 60)
        print("1. Upload single file")
        print("2. Upload multiple files")
        print("3. List issue attachments")
        print("4. Upload geometry crawl files to UBG-2816")
        print("5. Exit")
        
        choice = input("\nSelect option (1-5): ").strip()
        
        if choice == "1":
            issue_key = input("Enter issue key (e.g., UBG-2816): ").strip()
            file_path = input("Enter file path: ").strip()
            
            if issue_key and file_path:
                uploader.upload_file(issue_key, file_path)
            else:
                print("❌ Issue key and file path are required!")
        
        elif choice == "2":
            issue_key = input("Enter issue key (e.g., UBG-2816): ").strip()
            print("Enter file paths (one per line, empty line to finish):")
            
            file_paths = []
            while True:
                path = input().strip()
                if not path:
                    break
                file_paths.append(path)
            
            if issue_key and file_paths:
                uploader.upload_multiple_files(issue_key, file_paths)
            else:
                print("❌ Issue key and at least one file path are required!")
        
        elif choice == "3":
            issue_key = input("Enter issue key (e.g., UBG-2816): ").strip()
            if issue_key:
                uploader.get_issue_attachments(issue_key)
            else:
                print("❌ Issue key is required!")
        
        elif choice == "4":
            # Predefined files for geometry crawl
            geometry_files = [
                "crawl_geometry.py",
                "crawl_single_geometry.py", 
                "crawl_missing_geometry.py",
                "create_ward_geometry_table.sql",
                "sample_geometry.json"
            ]
            
            # Check which files exist
            existing_files = []
            for file_path in geometry_files:
                if os.path.exists(file_path):
                    existing_files.append(file_path)
                else:
                    print(f"⚠️ File not found: {file_path}")
            
            if existing_files:
                print(f"📋 Found {len(existing_files)} files to upload:")
                for f in existing_files:
                    print(f"  - {f}")
                
                confirm = input("\nUpload these files to UBG-2816? (y/n): ").strip().lower()
                if confirm == 'y':
                    uploader.upload_multiple_files("UBG-2816", existing_files)
            else:
                print("❌ No geometry crawl files found!")
        
        elif choice == "5":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid option!")

if __name__ == "__main__":
    main()
