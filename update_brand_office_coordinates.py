#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import subprocess
import time
import json
import csv
from datetime import datetime
import urllib.parse

class BrandOfficeGeocoder:
    def __init__(self, api_key=None):
        # Google Maps Geocoding API
        self.api_key = api_key or "YOUR_GOOGLE_MAPS_API_KEY_HERE"
        self.geocoding_url = "https://maps.googleapis.com/maps/api/geocode/json"
        
        # Database config
        self.db_config = {
            'user': 'root',
            'password': 'root',
            'host': 'localhost',
            'port': '3306',
            'database': 'urbox'
        }
        
        # Rate limiting
        self.request_delay = 0.1  # 100ms delay between requests
        self.max_retries = 3
    
    def execute_query(self, query):
        """Thực thi query MySQL và trả về kết quả"""
        try:
            cmd = [
                'mysql', '-u', self.db_config['user'], 
                f"-p{self.db_config['password']}", 
                '-h', self.db_config['host'], 
                '-P', self.db_config['port'],
                '-D', self.db_config['database'], 
                '-e', query
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Lỗi query: {result.stderr}")
                return None
            
            return result.stdout.strip()
            
        except Exception as e:
            print(f"❌ Exception execute query: {e}")
            return None
    
    def parse_query_result(self, result_text):
        """Parse kết quả query thành list of dict"""
        if not result_text:
            return []
        
        lines = result_text.split('\n')
        if len(lines) < 2:
            return []
        
        # Lấy header
        headers = lines[0].split('\t')
        
        # Parse data
        data = []
        for line in lines[1:]:
            if line.strip():
                values = line.split('\t')
                if len(values) == len(headers):
                    row = {}
                    for i, header in enumerate(headers):
                        row[header] = values[i] if values[i] != 'NULL' else None
                    data.append(row)
        
        return data
    
    def get_brand_office_data(self, limit=None, offset=0):
        """Lấy dữ liệu brand_office cần cập nhật coordinates"""
        print("🔍 Lấy dữ liệu brand_office...")
        
        # Base query
        query = """
        SELECT id, address_old, latitude, longitude, name
        FROM brand_office 
        WHERE address_old IS NOT NULL 
        AND address_old != ''
        """
        
        # Add conditions for records without coordinates
        query += " AND (latitude IS NULL OR longitude IS NULL OR latitude = 0 OR longitude = 0)"
        
        # Add limit and offset
        if limit:
            query += f" LIMIT {limit}"
        if offset:
            query += f" OFFSET {offset}"
        
        result = self.execute_query(query)
        if result:
            data = self.parse_query_result(result)
            print(f"   Tìm thấy {len(data)} brand_office cần cập nhật coordinates")
            return data
        return []
    
    def geocode_address(self, address):
        """Sử dụng Google Maps API để lấy coordinates từ địa chỉ"""
        if not address or address.strip() == '':
            return None, None, "Địa chỉ trống"
        
        # Chuẩn bị parameters
        params = {
            'address': address,
            'key': self.api_key,
            'language': 'vi',  # Vietnamese
            'region': 'vn'     # Vietnam
        }
        
        for attempt in range(self.max_retries):
            try:
                print(f"   🌍 Geocoding: {address[:50]}...")
                
                # Gửi request
                response = requests.get(self.geocoding_url, params=params, timeout=10)
                
                if response.status_code != 200:
                    print(f"   ❌ HTTP Error: {response.status_code}")
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                
                data = response.json()
                
                # Kiểm tra status
                if data['status'] == 'OK' and data['results']:
                    location = data['results'][0]['geometry']['location']
                    lat = location['lat']
                    lng = location['lng']
                    
                    print(f"   ✅ Found: {lat}, {lng}")
                    return lat, lng, "Success"
                
                elif data['status'] == 'ZERO_RESULTS':
                    return None, None, "Không tìm thấy địa chỉ"
                
                elif data['status'] == 'OVER_QUERY_LIMIT':
                    print(f"   ⚠️ Over query limit, waiting...")
                    time.sleep(1)
                    continue
                
                else:
                    return None, None, f"API Error: {data['status']}"
                
            except requests.exceptions.RequestException as e:
                print(f"   ❌ Request error: {e}")
                time.sleep(self.request_delay * (attempt + 1))
                continue
            
            except Exception as e:
                print(f"   ❌ Exception: {e}")
                return None, None, f"Exception: {e}"
        
        return None, None, "Max retries exceeded"
    
    def update_brand_office_coordinates(self, office_id, latitude, longitude):
        """Cập nhật coordinates vào database"""
        try:
            query = f"""
            UPDATE brand_office 
            SET latitude = {latitude}, longitude = {longitude}
            WHERE id = {office_id}
            """
            
            result = self.execute_query(query)
            return result is not None
            
        except Exception as e:
            print(f"❌ Exception update coordinates: {e}")
            return False
    
    def export_results_csv(self, results, filename):
        """Xuất kết quả ra file CSV"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'office_id', 'name', 'address_old', 'old_latitude', 'old_longitude',
                    'new_latitude', 'new_longitude', 'status', 'error_message'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    writer.writerow(result)
            
            print(f"✅ Đã xuất kết quả ra file: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi xuất file CSV: {e}")
            return False
    
    def run_geocoding_update(self, limit=None, dry_run=False):
        """Chạy cập nhật coordinates cho brand_office"""
        print("🚀 BẮT ĐẦU CẬP NHẬT COORDINATES CHO BRAND_OFFICE")
        print("=" * 60)
        
        if self.api_key == "YOUR_GOOGLE_MAPS_API_KEY_HERE":
            print("⚠️ CẢNH BÁO: Chưa cấu hình Google Maps API key!")
            print("   Vui lòng cập nhật API key trong code hoặc truyền vào constructor")
            if not dry_run:
                return
        
        # Lấy dữ liệu brand_office
        offices = self.get_brand_office_data(limit=limit)
        if not offices:
            print("❌ Không có dữ liệu brand_office để xử lý")
            return
        
        print(f"📋 Sẽ xử lý {len(offices)} brand_office")
        if dry_run:
            print("🔍 DRY RUN MODE - Chỉ test geocoding, không cập nhật database")
        print()
        
        # Thống kê
        results = []
        total_success = 0
        total_failed = 0
        
        for i, office in enumerate(offices, 1):
            office_id = office['id']
            name = office['name'] or f"Office {office_id}"
            address = office['address_old']
            old_lat = office['latitude']
            old_lng = office['longitude']
            
            print(f"[{i}/{len(offices)}] 🏢 {name}")
            print(f"   📍 Địa chỉ: {address}")
            print(f"   📊 Coordinates cũ: {old_lat}, {old_lng}")
            
            # Geocoding
            lat, lng, status = self.geocode_address(address)
            
            result = {
                'office_id': office_id,
                'name': name,
                'address_old': address,
                'old_latitude': old_lat,
                'old_longitude': old_lng,
                'new_latitude': lat,
                'new_longitude': lng,
                'status': 'Success' if lat and lng else 'Failed',
                'error_message': status if not (lat and lng) else ''
            }
            
            if lat and lng:
                if not dry_run:
                    # Cập nhật database
                    if self.update_brand_office_coordinates(office_id, lat, lng):
                        print(f"   ✅ Đã cập nhật coordinates: {lat}, {lng}")
                        total_success += 1
                    else:
                        print(f"   ❌ Lỗi cập nhật database")
                        result['status'] = 'Failed'
                        result['error_message'] = 'Database update failed'
                        total_failed += 1
                else:
                    print(f"   ✅ Geocoding thành công: {lat}, {lng} (DRY RUN)")
                    total_success += 1
            else:
                print(f"   ❌ Geocoding thất bại: {status}")
                total_failed += 1
            
            results.append(result)
            
            # Delay để tránh rate limit
            if i < len(offices):
                time.sleep(self.request_delay)
            
            print()
        
        # Báo cáo kết quả
        print("=" * 60)
        print("📊 KẾT QUẢ CẬP NHẬT COORDINATES")
        print("=" * 60)
        print(f"✅ Thành công: {total_success}")
        print(f"❌ Thất bại: {total_failed}")
        print(f"📋 Tổng cộng: {len(offices)}")
        
        # Xuất CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"brand_office_geocoding_{timestamp}.csv"
        
        if self.export_results_csv(results, filename):
            print(f"📄 Đã xuất báo cáo chi tiết ra file: {filename}")
        
        return results

def main():
    """Hàm main"""
    print("🔧 BRAND OFFICE GEOCODING TOOL")
    print("=" * 60)
    
    # Tùy chọn
    print("Tùy chọn:")
    print("1. Test với 5 records (dry run)")
    print("2. Test với 10 records (dry run)")
    print("3. Cập nhật thực tế 10 records")
    print("4. Cập nhật thực tế tất cả")
    print("5. Nhập API key và chạy")
    
    choice = input("Chọn (1-5): ").strip()
    
    geocoder = BrandOfficeGeocoder()
    
    if choice == "1":
        geocoder.run_geocoding_update(limit=5, dry_run=True)
    elif choice == "2":
        geocoder.run_geocoding_update(limit=10, dry_run=True)
    elif choice == "3":
        geocoder.run_geocoding_update(limit=10, dry_run=False)
    elif choice == "4":
        geocoder.run_geocoding_update(dry_run=False)
    elif choice == "5":
        api_key = input("Nhập Google Maps API key: ").strip()
        if api_key:
            geocoder = BrandOfficeGeocoder(api_key=api_key)
            limit = input("Giới hạn số records (Enter = tất cả): ").strip()
            limit = int(limit) if limit.isdigit() else None
            geocoder.run_geocoding_update(limit=limit, dry_run=False)
        else:
            print("❌ API key không hợp lệ")
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
