# Hướng dẫn tạo Jira API Token

## 1. Tạo Jira API Token

### Bước 1: <PERSON><PERSON><PERSON> cập <PERSON>sian Account Settings
- <PERSON><PERSON> tới: https://id.atlassian.com/manage-profile/security/api-tokens
- Đăng nhập bằng tài khoản Atlassian của bạn

### Bước 2: Tạo API Token
- Click **"Create API token"**
- Nhập **Label** cho token (ví dụ: "File Upload Tool")
- Click **"Create"**
- **Copy token ngay lập tức** (chỉ hiển thị 1 lần)

### Bước 3: L<PERSON><PERSON> trữ an toàn
- Lưu token vào file text hoặc password manager
- **Không chia sẻ token với ai khác**
- **Không commit token vào git**

## 2. Thông tin cần thiết

### Để sử dụng tool upload, bạn cần:
1. **Jira Base URL**: https://urbox.atlassian.net
2. **Email**: <PERSON>ail đăng nhập <PERSON>ra của bạn
3. **API Token**: Token vừa tạo ở bước 1

## 3. Sử dụng Tool Upload

### Chạy tool:
```bash
python3 jira_file_uploader.py
```

### Menu options:
1. **Upload single file** - Upload 1 file
2. **Upload multiple files** - Upload nhiều file
3. **List issue attachments** - Xem file đã upload
4. **Upload geometry crawl files** - Upload tự động các file geometry
5. **Exit** - Thoát

### Ví dụ sử dụng:
```
Enter Jira Base URL: https://urbox.atlassian.net
Enter your Jira email: <EMAIL>
Enter your Jira API token: ATATT3xFfGF0...
```

## 4. Files sẽ được upload cho UBG-2816

### Geometry Crawl Files:
- `crawl_geometry.py` - Tool chính crawl geometry
- `crawl_single_geometry.py` - Tool crawl 1 xã cụ thể  
- `crawl_missing_geometry.py` - Tool crawl xã còn thiếu
- `create_ward_geometry_table.sql` - Script tạo bảng
- `sample_geometry.json` - Dữ liệu mẫu

## 5. Troubleshooting

### Lỗi thường gặp:

#### "401 Unauthorized"
- **Nguyên nhân**: Email hoặc API token không đúng
- **Giải pháp**: Kiểm tra lại email và tạo token mới

#### "403 Forbidden" 
- **Nguyên nhân**: Không có quyền upload file vào issue
- **Giải pháp**: Liên hệ admin để cấp quyền

#### "404 Not Found"
- **Nguyên nhân**: Issue key không tồn tại hoặc sai
- **Giải pháp**: Kiểm tra lại issue key (ví dụ: UBG-2816)

#### "413 Request Entity Too Large"
- **Nguyên nhân**: File quá lớn (limit thường là 10MB)
- **Giải pháp**: Nén file hoặc chia nhỏ

### Debug tips:
- Kiểm tra Base URL không có dấu `/` cuối
- Đảm bảo email chính xác (case sensitive)
- Test với issue bạn có quyền edit

## 6. Security Best Practices

### API Token Security:
- **Rotate token định kỳ** (3-6 tháng)
- **Revoke token** khi không sử dụng
- **Sử dụng environment variables** thay vì hardcode

### Environment Variables (Optional):
```bash
export JIRA_BASE_URL="https://urbox.atlassian.net"
export JIRA_EMAIL="<EMAIL>"  
export JIRA_API_TOKEN="your_token_here"
```

### Sử dụng với env vars:
```python
import os

base_url = os.getenv('JIRA_BASE_URL')
email = os.getenv('JIRA_EMAIL')
api_token = os.getenv('JIRA_API_TOKEN')
```

## 7. Advanced Usage

### Upload programmatically:
```python
from jira_file_uploader import JiraFileUploader

uploader = JiraFileUploader(
    base_url="https://urbox.atlassian.net",
    email="<EMAIL>", 
    api_token="your_token"
)

# Upload single file
result = uploader.upload_file("UBG-2816", "crawl_geometry.py")

# Upload multiple files
files = ["file1.py", "file2.sql", "file3.json"]
results = uploader.upload_multiple_files("UBG-2816", files)
```

### Batch upload script:
```python
#!/usr/bin/env python3
import glob
from jira_file_uploader import JiraFileUploader

uploader = JiraFileUploader(base_url, email, token)

# Upload all Python files
python_files = glob.glob("*.py")
uploader.upload_multiple_files("UBG-2816", python_files)

# Upload all SQL files  
sql_files = glob.glob("*.sql")
uploader.upload_multiple_files("UBG-2816", sql_files)
```

## 8. File Size Limits

### Jira Cloud Limits:
- **Maximum file size**: 10 MB per file
- **Total attachments**: 100 MB per issue
- **File types**: Most types allowed (exe, bat restricted)

### Large files workaround:
- Compress files with zip/tar.gz
- Split large files into chunks
- Use external storage (Google Drive, Dropbox) + link

## 9. Monitoring & Logs

### Tool provides:
- **Real-time progress** during upload
- **Success/failure status** for each file
- **File size and attachment ID** info
- **Summary statistics** after batch upload

### Example output:
```
📁 Uploading file: crawl_geometry.py
📊 File size: 15,234 bytes
🎯 Target issue: UBG-2816
✅ Upload successful!
📎 Attachment ID: 12345
🔗 Download URL: https://urbox.atlassian.net/...
```

## 10. Integration with CI/CD

### GitHub Actions example:
```yaml
- name: Upload files to Jira
  run: |
    python3 jira_file_uploader.py
  env:
    JIRA_BASE_URL: ${{ secrets.JIRA_BASE_URL }}
    JIRA_EMAIL: ${{ secrets.JIRA_EMAIL }}
    JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
```
