# Jira File Upload Test Tool

🧪 Tool để test khả năng upload file lên Jira với OAuth authentication

## 📋 Quyền cần thiết

Bạn đã có đầy đủ quyền:
- ✅ `read:jira-work`
- ✅ `write:jira-work` 
- ✅ `read:attachment:jira`
- ✅ `write:attachment:jira`
- ✅ `delete:attachment:jira`

## 🚀 Cách sử dụng

### Bước 1: Cấu hình

Chỉnh sửa file `jira_config.py`:

```python
# Thông tin Jira instance
JIRA_URL = "https://urbox.atlassian.net"

# OAuth Token của bạn
OAUTH_TOKEN = "YOUR_OAUTH_TOKEN_HERE"

# Cloud ID (nếu có)
CLOUD_ID = "YOUR_CLOUD_ID_HERE"

# Issue để test
TEST_ISSUE_KEY = "UBG-2792"
```

### Bước 2: Chạy test

#### Test đơn giản:
```bash
python jira_upload_test.py
```

#### Test nâng cao:
```bash
# Test cơ bản
python jira_upload_advanced.py

# Test với debug mode
python jira_upload_advanced.py --debug

# Test với issue khác
python jira_upload_advanced.py --issue UBG-2790

# Test với file khác
python jira_upload_advanced.py --file my_document.pdf

# Test với token khác
python jira_upload_advanced.py --token YOUR_TOKEN
```

## 📁 Files được tạo

1. **`jira_upload_test.py`** - Tool test cơ bản
2. **`jira_upload_advanced.py`** - Tool test nâng cao với nhiều tùy chọn
3. **`jira_config.py`** - File cấu hình
4. **`test_upload.txt`** - File test sẽ được tạo tự động

## 🔧 Troubleshooting

### Lỗi Authentication
- Kiểm tra OAuth token có đúng không
- Kiểm tra token có hết hạn không
- Kiểm tra Cloud ID (nếu sử dụng)

### Lỗi Permission
- Kiểm tra quyền `write:attachment:jira`
- Kiểm tra quyền truy cập issue

### Lỗi File
- Kiểm tra file có tồn tại không
- Kiểm tra kích thước file (Jira có giới hạn)
- Kiểm tra định dạng file

## 📊 Output mẫu

```
🚀 Starting Full Upload Test
============================================================
🔐 Testing connection...
✅ Connection successful!
👤 User: Your Name
📧 Email: <EMAIL>

----------------------------------------
📋 Getting issue details: UBG-2792
✅ Issue found!
📝 Summary: Research thông tin và phân tích phương án
🏷️ Project: UBG
📎 Current attachments: 1

----------------------------------------
📎 Uploading file: test_upload.txt to UBG-2792
📄 File: test_upload.txt
📏 Size: 245 bytes
🔄 Uploading...
📊 Response Status: 200
🎉 Upload successful!
🆔 Attachment ID: 23871
📄 Filename: test_upload.txt
📏 Size: 245 bytes

============================================================
🎉 FULL TEST PASSED!
✅ File upload to Jira is working correctly!
```

## 🎯 Mục đích

Tool này giúp:
1. ✅ Verify OAuth authentication
2. ✅ Test quyền truy cập Jira
3. ✅ Test upload file attachment
4. ✅ Debug các vấn đề kết nối
5. ✅ Cung cấp thông tin chi tiết về lỗi

## 🔗 API Endpoints sử dụng

- `GET /rest/api/3/myself` - Test authentication
- `GET /rest/api/3/issue/{issueKey}` - Get issue info
- `POST /rest/api/3/issue/{issueKey}/attachments` - Upload attachment

## 📝 Notes

- Tool sử dụng `requests` library với `multipart/form-data`
- Hỗ trợ OAuth 2.0 authentication
- Có debug mode để troubleshoot
- Tự động tạo test file nếu chưa có
- Hiển thị thông tin chi tiết về response
