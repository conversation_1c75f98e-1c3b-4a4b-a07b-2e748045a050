#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import subprocess
import json

def export_ward_merge():
    """Export dữ liệu từ bảng ward với is_merge = 2 ra file ward_merge.csv"""
    
    print("🔄 Export dữ liệu ward với is_merge = 2...")
    
    # Câu lệnh SQL để lấy dữ liệu
    sql_query = """
    SELECT id, province_id, pti_id, title, prefix, title_full 
    FROM ward 
    WHERE is_merge = 2 
    ORDER BY province_id, id
    """
    
    try:
        # Chạy MySQL command để lấy dữ liệu dạng JSON
        cmd = [
            'mysql', 
            '-u', 'root', 
            '-proot', 
            '-h', 'localhost', 
            '-P', '3306',
            '-D', 'urbox',
            '--batch',
            '--raw',
            '-e', sql_query
        ]
        
        print("📊 Đang truy vấn dữ liệu từ database...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode != 0:
            print(f"❌ Lỗi MySQL: {result.stderr}")
            return False
        
        # Parse kết quả
        lines = result.stdout.strip().split('\n')
        if len(lines) < 2:
            print("❌ Không có dữ liệu")
            return False
        
        # Dòng đầu là header, bỏ qua
        headers = lines[0].split('\t')
        data_lines = lines[1:]
        
        print(f"✅ Đã lấy {len(data_lines)} records")
        print(f"📋 Columns: {', '.join(headers)}")
        
        # Ghi ra file CSV
        with open('ward_merge.csv', 'w', encoding='utf-8', newline='') as csvfile:
            writer = csv.writer(csvfile)
            
            # Ghi header
            writer.writerow(['id', 'province_id', 'pti_id', 'title', 'prefix', 'title_full'])
            
            # Ghi dữ liệu
            for line in data_lines:
                fields = line.split('\t')
                if len(fields) >= 6:
                    # Xử lý NULL values
                    processed_fields = []
                    for field in fields[:6]:  # Chỉ lấy 6 cột đầu
                        if field == 'NULL' or field == '\\N':
                            processed_fields.append('')
                        else:
                            processed_fields.append(field)
                    writer.writerow(processed_fields)
        
        print(f"💾 Đã export thành công ra file: ward_merge.csv")
        
        # Hiển thị một vài dòng mẫu
        print("\n📋 Preview 5 dòng đầu tiên:")
        with open('ward_merge.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for i, row in enumerate(reader):
                if i >= 6:  # Header + 5 dòng dữ liệu
                    break
                if i == 0:
                    print(f"   Header: {', '.join(row)}")
                else:
                    print(f"   Row {i}: ID={row[0]}, Province={row[1]}, PTI={row[2]}, Title={row[3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi export: {e}")
        return False

if __name__ == "__main__":
    export_ward_merge()
