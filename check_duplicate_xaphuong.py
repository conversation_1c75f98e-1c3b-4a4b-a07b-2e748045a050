#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import csv
import json
from datetime import datetime

class DuplicateChecker:
    def __init__(self):
        self.db_config = {
            'user': 'root',
            'password': 'root',
            'host': 'localhost',
            'port': '3306',
            'database': 'urbox'
        }
    
    def execute_query(self, query):
        """Thực thi query MySQL và trả về kết quả"""
        try:
            cmd = [
                'mysql', '-u', self.db_config['user'], 
                f"-p{self.db_config['password']}", 
                '-h', self.db_config['host'], 
                '-P', self.db_config['port'],
                '-D', self.db_config['database'], 
                '-e', query
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Lỗi query: {result.stderr}")
                return None
            
            return result.stdout.strip()
            
        except Exception as e:
            print(f"❌ Exception execute query: {e}")
            return None
    
    def parse_query_result(self, result_text):
        """Parse kết quả query thành list of dict"""
        if not result_text:
            return []
        
        lines = result_text.split('\n')
        if len(lines) < 2:
            return []
        
        # Lấy header
        headers = lines[0].split('\t')
        
        # Parse data
        data = []
        for line in lines[1:]:
            if line.strip():
                values = line.split('\t')
                if len(values) == len(headers):
                    row = {}
                    for i, header in enumerate(headers):
                        row[header] = values[i] if values[i] != 'NULL' else None
                    data.append(row)
        
        return data
    
    def check_duplicate_maxa(self):
        """Kiểm tra trùng lặp theo maxa"""
        print("🔍 Kiểm tra trùng lặp theo maxa...")
        
        query = """
        SELECT maxa, COUNT(*) as count, GROUP_CONCAT(id) as ids, GROUP_CONCAT(tenhc SEPARATOR ' | ') as names
        FROM xaphuong 
        WHERE maxa IS NOT NULL 
        GROUP BY maxa 
        HAVING COUNT(*) > 1 
        ORDER BY count DESC, maxa
        """
        
        result = self.execute_query(query)
        if result:
            data = self.parse_query_result(result)
            print(f"   Tìm thấy {len(data)} maxa bị trùng lặp")
            return data
        return []
    
    def check_duplicate_ma(self):
        """Kiểm tra trùng lặp theo mã xã phường"""
        print("🔍 Kiểm tra trùng lặp theo mã xã phường...")
        
        query = """
        SELECT ma, COUNT(*) as count, GROUP_CONCAT(id) as ids, GROUP_CONCAT(tenhc SEPARATOR ' | ') as names
        FROM xaphuong 
        WHERE ma IS NOT NULL AND ma != ''
        GROUP BY ma 
        HAVING COUNT(*) > 1 
        ORDER BY count DESC, ma
        """
        
        result = self.execute_query(query)
        if result:
            data = self.parse_query_result(result)
            print(f"   Tìm thấy {len(data)} mã xã phường bị trùng lặp")
            return data
        return []
    
    def check_duplicate_tenhc(self):
        """Kiểm tra trùng lặp theo tên xã phường"""
        print("🔍 Kiểm tra trùng lặp theo tên xã phường...")
        
        query = """
        SELECT tenhc, COUNT(*) as count, GROUP_CONCAT(id) as ids, GROUP_CONCAT(ma SEPARATOR ' | ') as codes
        FROM xaphuong 
        WHERE tenhc IS NOT NULL AND tenhc != ''
        GROUP BY tenhc 
        HAVING COUNT(*) > 1 
        ORDER BY count DESC, tenhc
        """
        
        result = self.execute_query(query)
        if result:
            data = self.parse_query_result(result)
            print(f"   Tìm thấy {len(data)} tên xã phường bị trùng lặp")
            return data
        return []
    
    def get_duplicate_details(self, ids_str):
        """Lấy thông tin chi tiết của các record trùng lặp"""
        if not ids_str:
            return []
        
        query = f"""
        SELECT id, matinh, ma, tenhc, maxa 
        FROM xaphuong 
        WHERE id IN ({ids_str})
        ORDER BY id
        """
        
        result = self.execute_query(query)
        if result:
            return self.parse_query_result(result)
        return []
    
    def export_to_csv(self, duplicate_data, filename):
        """Xuất dữ liệu trùng lặp ra file CSV"""
        print(f"📄 Xuất dữ liệu ra file: {filename}")
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'duplicate_type', 'duplicate_value', 'duplicate_count', 
                    'record_id', 'matinh', 'ma', 'tenhc', 'maxa'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                # Ghi header
                writer.writeheader()
                
                # Ghi dữ liệu
                for item in duplicate_data:
                    duplicate_type = item['type']
                    duplicate_value = item['value']
                    duplicate_count = item['count']
                    
                    # Lấy chi tiết các record trùng lặp
                    details = self.get_duplicate_details(item['ids'])
                    
                    for detail in details:
                        writer.writerow({
                            'duplicate_type': duplicate_type,
                            'duplicate_value': duplicate_value,
                            'duplicate_count': duplicate_count,
                            'record_id': detail['id'],
                            'matinh': detail['matinh'],
                            'ma': detail['ma'],
                            'tenhc': detail['tenhc'],
                            'maxa': detail['maxa']
                        })
            
            print(f"✅ Đã xuất thành công ra file: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi xuất file CSV: {e}")
            return False
    
    def run_duplicate_check(self):
        """Chạy kiểm tra trùng lặp toàn bộ"""
        print("🚀 BẮT ĐẦU KIỂM TRA TRÙNG LẶP DỮ LIỆU XAPHUONG")
        print("=" * 60)
        
        # Thống kê tổng quan
        total_query = "SELECT COUNT(*) as total FROM xaphuong"
        total_result = self.execute_query(total_query)
        if total_result:
            total_data = self.parse_query_result(total_result)
            if total_data:
                print(f"📊 Tổng số record trong bảng xaphuong: {total_data[0]['total']}")
        
        print()
        
        # Kiểm tra từng loại trùng lặp
        all_duplicates = []
        
        # 1. Trùng lặp theo maxa
        maxa_duplicates = self.check_duplicate_maxa()
        for item in maxa_duplicates:
            all_duplicates.append({
                'type': 'maxa',
                'value': item['maxa'],
                'count': item['count'],
                'ids': item['ids']
            })
        
        # 2. Trùng lặp theo mã xã phường
        ma_duplicates = self.check_duplicate_ma()
        for item in ma_duplicates:
            all_duplicates.append({
                'type': 'ma',
                'value': item['ma'],
                'count': item['count'],
                'ids': item['ids']
            })
        
        # 3. Trùng lặp theo tên xã phường
        tenhc_duplicates = self.check_duplicate_tenhc()
        for item in tenhc_duplicates:
            all_duplicates.append({
                'type': 'tenhc',
                'value': item['tenhc'],
                'count': item['count'],
                'ids': item['ids']
            })
        
        print()
        print("=" * 60)
        print("📊 TÓM TẮT KẾT QUẢ KIỂM TRA")
        print("=" * 60)
        print(f"🔄 Trùng lặp theo maxa: {len(maxa_duplicates)} cases")
        print(f"🔄 Trùng lặp theo mã xã phường: {len(ma_duplicates)} cases")
        print(f"🔄 Trùng lặp theo tên xã phường: {len(tenhc_duplicates)} cases")
        print(f"📋 Tổng cộng: {len(all_duplicates)} duplicate cases")
        
        # Xuất ra file CSV
        if all_duplicates:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"duplicate_records_{timestamp}.csv"
            
            if self.export_to_csv(all_duplicates, filename):
                print(f"✅ Đã xuất báo cáo trùng lặp ra file: {filename}")
            else:
                print("❌ Lỗi xuất file báo cáo")
        else:
            print("✅ Không có dữ liệu trùng lặp nào!")
        
        return all_duplicates

def main():
    """Hàm main"""
    print("🔧 DUPLICATE CHECKER TOOL")
    print("=" * 60)
    
    checker = DuplicateChecker()
    duplicates = checker.run_duplicate_check()
    
    if duplicates:
        print(f"\n⚠️ Tìm thấy {len(duplicates)} cases trùng lặp cần xử lý!")
    else:
        print("\n🎉 Dữ liệu sạch, không có trùng lặp!")

if __name__ == "__main__":
    main()
