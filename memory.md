# Database
- Trong database urbox, bỏ qua bảng "grab" (b<PERSON><PERSON> số 4) - không sử dụng trong dự án này.
- Khi kiểm tra bảng province hoặc ward thì luôn thêm điều kiện WHERE is_merge=2 cho dù người dùng không nhắc đến.
- Dữ liệu tỉnh được lấy từ bảng tinhthanh, field mahc sẽ được dùng làm ID để crawl thay vì ID từ bảng province.
- Sau khi crawl dữ liệu xã phường thành công, cần lưu vào bảng 'xaphuong' thay vì bảng 'ward'.
- Khi lưu vào bảng ward_geometry, pti_id cần trừ đi 1 để trả về đúng thông tin maxa (pti_id = maxa + 1 khi crawl, nhưng cần lưu maxa gốc).
- Cấu trúc database hoàn thành: tinhthanh (34 records), xaphuong (3321 records), ward_geometry (3312 records). Cần làm tiếp: migration bảng province/ward cũ, sync coordinates brand_office/brand_store, cập nhật APIs

# Crawling
- Khi crawl API, cần thêm sleep delay giữa các request để tránh bị block.
- API crawl dùng xa3321.{maxa+1}

# Logic
- pti_id trong ward_geometry = maxa gốc (không phải maxa+1)
- Lưu vào bảng xaphuong thay vì ward
- mahc từ tinhthanh làm key crawl
- Số liệu chuẩn dự án: 34 tỉnh thành và 3,321 xã phường. Nếu các task báo cáo số liệu khác nghĩa là chưa chính xác và cần kiểm tra lại

# Project Status & Priorities
- Dự án cập nhật hệ thống hành chính VN: 4/21 tasks hoàn thành (crawl 34 tỉnh, 3321 xã, 3312 geometry), 17 tasks còn lại (database migration + API development), database urbox, tools đã tạo: crawl_geometry.py, crawl_all_xaphuong.py, import_tinhthanh.py
- Tasks ưu tiên cao tiếp theo: Task 5 (cập nhật bảng province với is_merge=2), Task 6 (cập nhật bảng ward), Task 15-16 (API tỉnh/xã mới), Task 20 (API mobile app)
- Files báo cáo đã tạo: 00_TONG_QUAN_BAO_CAO.md, 01_BAO_CAO_TONG_QUAN_PM.md, 02_BAO_CAO_BUSINESS_BA.md, 03_BAO_CAO_TECHNICAL_DEV.md, 04_TASK_BREAKDOWN_DETAILED.md - timeline 5 tuần cho 17 tasks còn lại

# Jira & Documentation
- Jira tasks dự án: UBG-2792 (task chính cho báo cáo phân tích), UBG-2790 (epic tổng hợp). Upload báo cáo vào UBG-2792, tạo tasks mới vào epic UBG-2790
- Preferred approach for Jira documentation: Use Confluence pages for detailed reports when Jira file upload is not available, with links from Jira to Confluence.
- Jira file upload works with curl using Bearer token auth and -F flag for multipart form data: curl -X POST -H 'Authorization: Bearer <token>' -H 'X-Atlassian-Token: no-check' -F 'file=@/path/file' https://site.atlassian.net/rest/api/3/issue/key/attachments