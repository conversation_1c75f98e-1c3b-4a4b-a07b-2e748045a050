#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.request
import urllib.parse
import json
import subprocess
import sys

class SingleGeometryCrawler:
    def __init__(self):
        self.base_url = "https://sapnhap.bando.com.vn/pread_json"
        self.headers = {
            'Accept': 'text/plain, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Cookie': 'PHPSESSID=tncekeavtrlr9jtequvls32mtl',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    def get_xaphuong_info(self, maxa):
        """Lấy thông tin xã phường từ maxa"""
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', 
                f'SELECT id, matinh, ma, tenhc, maxa FROM xaphuong WHERE maxa = {maxa}'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Lỗi query database: {result.stderr}")
                return None
            
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:
                parts = lines[1].split('\t')
                if len(parts) >= 5:
                    return {
                        'id': int(parts[0]),
                        'matinh': int(parts[1]),
                        'ma': parts[2],
                        'tenhc': parts[3],
                        'maxa': int(parts[4])
                    }
            return None
            
        except Exception as e:
            print(f"❌ Lỗi get xaphuong info: {e}")
            return None
    
    def check_existing_geometry(self, maxa):
        """Kiểm tra xem đã có geometry data cho maxa này chưa"""
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e',
                f'SELECT COUNT(*) FROM ward_geometry WHERE pti_id = {maxa}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    count = int(lines[1])
                    return count > 0
            return False

        except Exception as e:
            print(f"❌ Lỗi check existing geometry: {e}")
            return False
    
    def delete_existing_geometry(self, maxa):
        """Xóa geometry data cũ nếu có"""
        try:
            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e',
                f'DELETE FROM ward_geometry WHERE pti_id = {maxa}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0

        except Exception as e:
            print(f"❌ Lỗi delete existing geometry: {e}")
            return False
    
    def crawl_geometry_data(self, api_id):
        """Crawl geometry data cho một api_id (maxa + 1)"""
        geometry_id = f"xa3321.{api_id}"
        print(f"🔄 Crawling geometry cho ID: {geometry_id}")

        try:
            # Tạo POST data
            data = urllib.parse.urlencode({'id': geometry_id}).encode('utf-8')

            # Tạo request
            req = urllib.request.Request(
                self.base_url,
                data=data,
                headers=self.headers
            )

            # Gửi request
            with urllib.request.urlopen(req, timeout=30) as response:
                if response.getcode() != 200:
                    print(f"❌ HTTP Error: {response.getcode()}")
                    return None

                # Đọc response
                response_data = response.read().decode('utf-8')

                # Kiểm tra response có hợp lệ không
                if not response_data or response_data.strip() == '':
                    print(f"⚠️ Response rỗng cho ID: {geometry_id}")
                    return None

                # Thử parse JSON để kiểm tra tính hợp lệ
                try:
                    json.loads(response_data)
                    print(f"✅ Crawl thành công geometry cho ID: {geometry_id}")
                    return response_data
                except json.JSONDecodeError:
                    print(f"⚠️ Response không phải JSON hợp lệ cho ID: {geometry_id}")
                    return response_data  # Vẫn lưu dù không phải JSON

        except Exception as e:
            print(f"❌ Exception crawl geometry: {e}")
            return None
    
    def save_geometry_to_db(self, maxa, geometry_data):
        """Lưu geometry data vào database với pti_id = maxa (giá trị gốc)"""
        if not geometry_data:
            return False

        try:
            # Escape JSON data cho SQL
            escaped_data = geometry_data.replace("'", "\\'").replace('"', '\\"')

            insert_sql = f"""INSERT INTO ward_geometry (pti_id, data) VALUES ({maxa}, '{escaped_data}')"""

            cmd = [
                'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                '-D', 'urbox', '-e', insert_sql
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return True
            else:
                print(f"❌ Lỗi insert geometry: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Exception save geometry: {e}")
            return False
    
    def crawl_single_maxa(self, maxa, force_update=False):
        """Crawl geometry cho một maxa cụ thể"""
        print("🚀 BẮT ĐẦU CRAWL GEOMETRY CHO MAXA ĐƠN LẺ")
        print("=" * 60)
        print(f"🎯 Target maxa: {maxa}")
        
        # Lấy thông tin xã phường
        xaphuong_info = self.get_xaphuong_info(maxa)
        if not xaphuong_info:
            print(f"❌ Không tìm thấy thông tin cho maxa: {maxa}")
            return False
        
        print(f"📍 Thông tin xã phường:")
        print(f"   - ID: {xaphuong_info['id']}")
        print(f"   - Mã tỉnh: {xaphuong_info['matinh']}")
        print(f"   - Mã: {xaphuong_info['ma']}")
        print(f"   - Tên: {xaphuong_info['tenhc']}")
        print(f"   - Maxa: {xaphuong_info['maxa']}")
        print()
        
        # Kiểm tra đã có geometry data chưa
        has_existing = self.check_existing_geometry(maxa)
        if has_existing:
            print(f"⚠️ Đã có geometry data cho maxa: {maxa}")
            if force_update:
                print("🔄 Force update = True, sẽ xóa data cũ và crawl lại...")
                if not self.delete_existing_geometry(maxa):
                    print("❌ Không thể xóa geometry data cũ!")
                    return False
                print("✅ Đã xóa geometry data cũ")
            else:
                print("❌ Sử dụng force_update=True để ghi đè")
                return False
        
        # Crawl geometry data
        api_id = maxa + 1  # maxa + 1 để gọi API
        print(f"🔄 Bắt đầu crawl với api_id: {api_id}")
        
        try:
            geometry_data = self.crawl_geometry_data(api_id)
            
            if geometry_data:
                # Lưu vào database
                if self.save_geometry_to_db(maxa, geometry_data):
                    print(f"✅ Thành công crawl và lưu geometry cho maxa: {maxa}")
                    print(f"📊 Dữ liệu geometry đã được lưu vào ward_geometry với pti_id: {maxa}")
                    return True
                else:
                    print(f"❌ Lỗi lưu geometry vào database!")
                    return False
            else:
                print(f"❌ Không lấy được geometry data cho maxa: {maxa}")
                return False
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            return False

def main():
    """Hàm main"""
    if len(sys.argv) < 2:
        print("❌ Cách sử dụng: python crawl_single_geometry.py <maxa> [force]")
        print("   Ví dụ: python crawl_single_geometry.py 613")
        print("   Ví dụ: python crawl_single_geometry.py 613 force")
        sys.exit(1)
    
    try:
        maxa = int(sys.argv[1])
        force_update = len(sys.argv) > 2 and sys.argv[2].lower() == 'force'
        
        print("🔧 CRAWL SINGLE GEOMETRY TOOL")
        print("=" * 60)
        
        crawler = SingleGeometryCrawler()
        success = crawler.crawl_single_maxa(maxa, force_update)
        
        if success:
            print("\n🎉 HOÀN THÀNH THÀNH CÔNG!")
        else:
            print("\n❌ THẤT BẠI!")
            sys.exit(1)
            
    except ValueError:
        print("❌ Maxa phải là số nguyên!")
        sys.exit(1)

if __name__ == "__main__":
    main()
