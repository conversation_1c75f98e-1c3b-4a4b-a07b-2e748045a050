# Jira File Uploader Tool

Tool để upload file lên <PERSON> issues một cách tự động và dễ dàng.

## 📁 Files đã tạo

1. **`jira_file_uploader.py`** - Tool ch<PERSON>h để upload file
2. **`upload_geometry_files.py`** - <PERSON>ript upload các file geometry crawl
3. **`JIRA_API_TOKEN_SETUP.md`** - Hướng dẫn tạo API token
4. **`README_JIRA_FILE_UPLOADER.md`** - File này

## 🚀 Cách sử dụng nhanh

### Bước 1: Tạo Jira API Token
```
1. Truy cập: https://id.atlassian.com/manage-profile/security/api-tokens
2. Click "Create API token"
3. Nhập label và tạo token
4. Copy token (chỉ hiển thị 1 lần!)
```

### Bước 2: Upload files geometry crawl
```bash
python3 upload_geometry_files.py
```

### Bước 3: Nhập thông tin khi được yêu cầu
```
Enter your Jira email: <EMAIL>
Enter your Jira API token: ATATT3xFfGF0...
```

## 🎯 Mục đích chính

### Upload files cho task UBG-2816 (Crawl geometry data):
- `crawl_geometry.py` - Tool chính crawl geometry
- `crawl_single_geometry.py` - Tool crawl 1 xã cụ thể  
- `crawl_missing_geometry.py` - Tool crawl xã còn thiếu
- `create_ward_geometry_table.sql` - Script tạo bảng
- `sample_geometry.json` - Dữ liệu mẫu

## 🔧 Tool chính (jira_file_uploader.py)

### Tính năng:
- ✅ Upload single file
- ✅ Upload multiple files
- ✅ List attachments của issue
- ✅ Batch upload với progress tracking
- ✅ Error handling và retry
- ✅ File size validation

### Menu options:
```
1. Upload single file
2. Upload multiple files  
3. List issue attachments
4. Upload geometry crawl files to UBG-2816
5. Exit
```

### Sử dụng programmatically:
```python
from jira_file_uploader import JiraFileUploader

uploader = JiraFileUploader(
    base_url="https://urbox.atlassian.net",
    email="<EMAIL>",
    api_token="your_token"
)

# Upload single file
result = uploader.upload_file("UBG-2816", "crawl_geometry.py")

# Upload multiple files
files = ["file1.py", "file2.sql"]
results = uploader.upload_multiple_files("UBG-2816", files)
```

## 📊 Output mẫu

```
🚀 UPLOAD GEOMETRY CRAWL FILES TO JIRA
============================================================
✅ Found: crawl_geometry.py (15,234 bytes)
✅ Found: crawl_single_geometry.py (8,456 bytes)
✅ Found: crawl_missing_geometry.py (7,123 bytes)
✅ Found: create_ward_geometry_table.sql (892 bytes)
✅ Found: sample_geometry.json (2,345 bytes)

📊 Summary: 5 found, 0 missing

🔐 Please provide your Jira credentials:
Enter your Jira email: <EMAIL>
Enter your Jira API token: ********

🎯 Target issue: UBG-2816
📋 Files to upload: 5
Proceed with upload? (y/n): y

🚀 Starting batch upload to UBG-2816
📋 Files to upload: 5

[1/5] Processing: crawl_geometry.py
📁 Uploading file: crawl_geometry.py
📊 File size: 15,234 bytes
🎯 Target issue: UBG-2816
✅ Upload successful!
📎 Attachment ID: 12345
🔗 Download URL: https://urbox.atlassian.net/...

============================================================
📊 UPLOAD SUMMARY
============================================================
✅ Successful: 5
❌ Failed: 0
📋 Total: 5

🎉 UPLOAD COMPLETED!
✅ Successful uploads: 5
❌ Failed uploads: 0

🔗 View issue: https://urbox.atlassian.net/browse/UBG-2816
```

## 🛡️ Security & Best Practices

### API Token Security:
- **Không commit token vào git**
- **Sử dụng environment variables**
- **Rotate token định kỳ**
- **Revoke token khi không dùng**

### Environment Variables (Optional):
```bash
export JIRA_BASE_URL="https://urbox.atlassian.net"
export JIRA_EMAIL="<EMAIL>"
export JIRA_API_TOKEN="your_token_here"

python3 jira_file_uploader.py
```

## 📏 File Limits

### Jira Cloud Limits:
- **Max file size**: 10 MB per file
- **Total attachments**: 100 MB per issue
- **Supported types**: Most file types (exe, bat restricted)

### Large files workaround:
- Compress với zip/tar.gz
- Split thành chunks nhỏ
- Sử dụng external storage + link

## 🐛 Troubleshooting

### Lỗi thường gặp:

#### "401 Unauthorized"
- **Nguyên nhân**: Email hoặc API token sai
- **Giải pháp**: Kiểm tra lại credentials

#### "403 Forbidden"
- **Nguyên nhân**: Không có quyền upload
- **Giải pháp**: Liên hệ admin cấp quyền

#### "404 Not Found"
- **Nguyên nhân**: Issue key không tồn tại
- **Giải pháp**: Kiểm tra lại issue key

#### "413 Request Entity Too Large"
- **Nguyên nhân**: File quá lớn (>10MB)
- **Giải pháp**: Nén file hoặc chia nhỏ

### Debug tips:
- Kiểm tra Base URL không có `/` cuối
- Email phải chính xác (case sensitive)
- Test với issue bạn có quyền edit

## 🔄 Workflow khuyến nghị

### Lần đầu sử dụng:
1. **Đọc hướng dẫn** trong `JIRA_API_TOKEN_SETUP.md`
2. **Tạo API token** từ Atlassian
3. **Test upload** với 1 file nhỏ
4. **Batch upload** các file cần thiết

### Sử dụng thường xuyên:
1. **Chạy script** `upload_geometry_files.py`
2. **Nhập credentials** khi được yêu cầu
3. **Confirm upload** và chờ hoàn thành
4. **Verify** trên Jira issue

## 🔗 Links hữu ích

- **Jira API Documentation**: https://developer.atlassian.com/cloud/jira/platform/rest/v3/
- **Create API Token**: https://id.atlassian.com/manage-profile/security/api-tokens
- **Jira Issue UBG-2816**: https://urbox.atlassian.net/browse/UBG-2816

## 📞 Support

Nếu gặp vấn đề:
1. **Check logs** trong console output
2. **Verify credentials** và permissions
3. **Test với file nhỏ** trước
4. **Check file size limits**
5. **Review error messages** chi tiết
