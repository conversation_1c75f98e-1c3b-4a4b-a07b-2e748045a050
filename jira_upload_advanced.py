#!/usr/bin/env python3
"""
Advanced Jira File Upload Test Tool
Sử dụng config file và có nhiều tùy chọn test
"""

import requests
import os
import json
import sys
import argparse
from pathlib import Path

# Import config
try:
    from jira_config import *
except ImportError:
    print("❌ Không tìm thấy jira_config.py!")
    print("💡 Vui lòng tạo file jira_config.py với thông tin Jira của bạn")
    sys.exit(1)

class AdvancedJiraUploader:
    def __init__(self, jira_url=None, oauth_token=None, cloud_id=None, debug=False):
        """Initialize với config từ file hoặc parameters"""
        self.jira_url = (jira_url or JIRA_URL).rstrip('/')
        self.oauth_token = oauth_token or OAUTH_TOKEN
        self.cloud_id = cloud_id or CLOUD_ID
        self.debug = debug or DEBUG
        
        if not self.oauth_token:
            raise ValueError("❌ OAuth token không được để trống!")
        
        # Setup headers
        self.headers = {
            'Authorization': f'Bearer {self.oauth_token}',
            'Accept': 'application/json',
            'X-Atlassian-Token': 'no-check'  # Bypass CSRF check
        }
        
        if self.cloud_id:
            self.headers['X-Atlassian-Cloud-Id'] = self.cloud_id
        
        if self.debug:
            print(f"🔧 Debug mode: ON")
            print(f"🌐 Jira URL: {self.jira_url}")
            print(f"🔑 Token: {self.oauth_token[:10]}...")
            if self.cloud_id:
                print(f"☁️ Cloud ID: {self.cloud_id}")
    
    def test_connection(self):
        """Test kết nối và quyền"""
        print("🔐 Testing connection...")
        
        url = f"{self.jira_url}/rest/api/3/myself"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if self.debug:
                print(f"📡 Request URL: {url}")
                print(f"📋 Request Headers: {json.dumps(dict(self.headers), indent=2)}")
                print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                user_info = response.json()
                print(f"✅ Connection successful!")
                print(f"👤 User: {user_info.get('displayName', 'Unknown')}")
                print(f"📧 Email: {user_info.get('emailAddress', 'Unknown')}")
                print(f"🆔 Account ID: {user_info.get('accountId', 'Unknown')}")
                return True
            else:
                print(f"❌ Connection failed: {response.status_code}")
                print(f"📝 Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"💥 Exception: {str(e)}")
            return False
    
    def get_issue_details(self, issue_key):
        """Lấy chi tiết issue"""
        print(f"📋 Getting issue details: {issue_key}")
        
        url = f"{self.jira_url}/rest/api/3/issue/{issue_key}"
        params = {
            'fields': 'summary,attachment,project'
        }
        
        try:
            response = requests.get(url, headers=self.headers, params=params, timeout=10)
            
            if self.debug:
                print(f"📡 Request URL: {url}")
                print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                issue = response.json()
                print(f"✅ Issue found!")
                print(f"📝 Summary: {issue['fields']['summary']}")
                print(f"🏷️ Project: {issue['fields']['project']['key']}")
                
                attachments = issue['fields'].get('attachment', [])
                print(f"📎 Current attachments: {len(attachments)}")
                
                if attachments and self.debug:
                    for att in attachments:
                        print(f"  - {att['filename']} ({att['size']} bytes)")
                
                return issue
            else:
                print(f"❌ Issue not found: {response.status_code}")
                print(f"📝 Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"💥 Exception: {str(e)}")
            return None
    
    def upload_file(self, issue_key, file_path):
        """Upload file to issue"""
        print(f"📎 Uploading file: {file_path} to {issue_key}")
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return False
        
        url = f"{self.jira_url}/rest/api/3/issue/{issue_key}/attachments"
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        print(f"📄 File: {file_name}")
        print(f"📏 Size: {file_size} bytes")
        
        try:
            with open(file_path, 'rb') as file:
                files = {
                    'file': (file_name, file, 'application/octet-stream')
                }
                
                # Headers cho upload (không có Content-Type)
                upload_headers = self.headers.copy()
                upload_headers.pop('Content-Type', None)
                
                if self.debug:
                    print(f"📡 Upload URL: {url}")
                    print(f"📋 Upload Headers: {json.dumps(dict(upload_headers), indent=2)}")
                
                print(f"🔄 Uploading...")
                response = requests.post(
                    url,
                    headers=upload_headers,
                    files=files,
                    timeout=60
                )
                
                print(f"📊 Response Status: {response.status_code}")
                
                if self.debug:
                    print(f"📋 Response Headers: {json.dumps(dict(response.headers), indent=2)}")
                    print(f"📝 Response Body: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    attachment = result[0]
                    
                    print(f"🎉 Upload successful!")
                    print(f"🆔 Attachment ID: {attachment['id']}")
                    print(f"📄 Filename: {attachment['filename']}")
                    print(f"📏 Size: {attachment['size']} bytes")
                    print(f"🔗 URL: {attachment['content']}")
                    
                    return True
                else:
                    print(f"❌ Upload failed: {response.status_code}")
                    print(f"📝 Error: {response.text}")
                    return False
                    
        except Exception as e:
            print(f"💥 Exception during upload: {str(e)}")
            return False
    
    def run_full_test(self, issue_key=None, file_path=None):
        """Chạy test đầy đủ"""
        print("🚀 Starting Full Upload Test")
        print("=" * 60)
        
        # Step 1: Test connection
        if not self.test_connection():
            print("❌ Connection test failed!")
            return False
        
        print("\n" + "-" * 40)
        
        # Step 2: Get issue details
        test_issue = issue_key or TEST_ISSUE_KEY
        issue = self.get_issue_details(test_issue)
        if not issue:
            print("❌ Issue test failed!")
            return False
        
        print("\n" + "-" * 40)
        
        # Step 3: Prepare test file
        test_file = file_path or TEST_FILE_PATH
        if not os.path.exists(test_file):
            print(f"📝 Creating test file: {test_file}")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("🧪 Jira Upload Test File\n")
                f.write("=" * 30 + "\n")
                f.write(f"Created: {os.popen('date').read().strip()}\n")
                f.write(f"Purpose: Testing file upload to Jira\n")
                f.write(f"Issue: {test_issue}\n")
                f.write(f"Tool: Advanced Jira Upload Tester\n")
                f.write("\n")
                f.write("This file tests the upload capability of the Jira REST API\n")
                f.write("with OAuth authentication and proper permissions.\n")
                f.write("\n")
                f.write("Permissions required:\n")
                f.write("- read:jira-work\n")
                f.write("- write:jira-work\n")
                f.write("- write:attachment:jira\n")
        
        # Step 4: Upload file
        success = self.upload_file(test_issue, test_file)
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 FULL TEST PASSED!")
            print("✅ File upload to Jira is working correctly!")
        else:
            print("💥 FULL TEST FAILED!")
            print("❌ File upload encountered errors!")
        
        return success

def main():
    """Main function với command line arguments"""
    parser = argparse.ArgumentParser(description='Advanced Jira File Upload Tester')
    parser.add_argument('--issue', '-i', help='Issue key to test (default: from config)')
    parser.add_argument('--file', '-f', help='File to upload (default: from config)')
    parser.add_argument('--debug', '-d', action='store_true', help='Enable debug mode')
    parser.add_argument('--token', '-t', help='OAuth token (default: from config)')
    
    args = parser.parse_args()
    
    try:
        # Initialize uploader
        uploader = AdvancedJiraUploader(
            oauth_token=args.token,
            debug=args.debug
        )
        
        # Run test
        success = uploader.run_full_test(
            issue_key=args.issue,
            file_path=args.file
        )
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"💥 Fatal error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
