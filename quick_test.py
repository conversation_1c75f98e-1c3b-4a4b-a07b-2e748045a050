#!/usr/bin/env python3
"""
Quick Jira Upload Test
Chạy ngay để test upload file lên Jira
"""

import requests
import os

def quick_upload_test():
    """Test nhanh upload file lên Jira"""
    
    print("🚀 Quick Jira Upload Test")
    print("=" * 40)
    
    # CẤU HÌNH - ĐIỀN THÔNG TIN CỦA BẠN
    JIRA_URL = "https://urbox.atlassian.net"
    EMAIL = input("📧 Nhập email Atlassian của bạn: ").strip()
    API_TOKEN = input("🔑 Nhập API Token của bạn: ").strip()

    if not EMAIL or not API_TOKEN:
        print("❌ Email và API Token không được để trống!")
        return

    ISSUE_KEY = "UBG-2792"
    
    # Tạo file test
    test_file = "quick_test_file.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write("🧪 Quick Test File Upload\n")
        f.write("Created by quick_test.py\n")
        f.write(f"Target issue: {ISSUE_KEY}\n")
    
    print(f"📝 Created test file: {test_file}")
    
    # Setup headers với Basic Auth
    import base64
    auth_string = f"{EMAIL}:{API_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

    headers = {
        'Authorization': f'Basic {auth_b64}',
        'Accept': 'application/json',
        'X-Atlassian-Token': 'no-check'
    }
    
    # Test connection
    print("🔐 Testing connection...")
    try:
        response = requests.get(f"{JIRA_URL}/rest/api/3/myself", headers=headers, timeout=10)
        if response.status_code != 200:
            print(f"❌ Connection failed: {response.status_code}")
            print(f"Error: {response.text}")
            return
        
        user = response.json()
        print(f"✅ Connected as: {user.get('displayName', 'Unknown')}")
        print(f"📧 Email: {user.get('emailAddress', 'Unknown')}")
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return
    
    # Upload file
    print(f"📎 Uploading to {ISSUE_KEY}...")
    try:
        url = f"{JIRA_URL}/rest/api/3/issue/{ISSUE_KEY}/attachments"
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file, f, 'text/plain')}
            
            response = requests.post(url, headers=headers, files=files, timeout=30)
            
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()[0]
                print("🎉 UPLOAD THÀNH CÔNG!")
                print(f"📄 File: {result['filename']}")
                print(f"🆔 ID: {result['id']}")
                print(f"📏 Size: {result['size']} bytes")
            else:
                print("❌ UPLOAD THẤT BẠI!")
                print(f"Error: {response.text}")
                
    except Exception as e:
        print(f"❌ Upload error: {e}")
    
    # Cleanup
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"🗑️ Cleaned up: {test_file}")

if __name__ == "__main__":
    quick_upload_test()
