#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import re
import subprocess

def load_ward_data():
    """Đọc dữ liệu từ ward_merge.csv"""
    ward_data = {}
    try:
        with open('ward_merge.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # Tạo key để tìm kiếm
                ward_title = row['title'].strip()
                prefix = row['prefix'].strip()
                province_id = int(row['province_id'])
                
                # Lưu thông tin ward
                if ward_title not in ward_data:
                    ward_data[ward_title] = []
                
                ward_data[ward_title].append({
                    'id': row['id'],
                    'province_id': province_id,
                    'pti_id': row['pti_id'],
                    'prefix': prefix,
                    'title': ward_title,
                    'title_full': row['title_full']
                })
        
        print(f"✅ Đã load {len(ward_data)} ward titles từ ward_merge.csv")
        return ward_data
    except Exception as e:
        print(f"❌ Lỗi khi đọc ward_merge.csv: {e}")
        return {}

def load_province_data():
    """Đọc dữ liệu province từ database"""
    province_data = {}
    try:
        cmd = [
            'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
            '-D', 'urbox', '--batch', '--raw',
            '-e', 'SELECT id, title FROM ___province WHERE is_megre = 2'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        if result.returncode != 0:
            print(f"❌ Lỗi MySQL: {result.stderr}")
            return {}
        
        lines = result.stdout.strip().split('\n')[1:]  # Bỏ header
        for line in lines:
            parts = line.split('\t')
            if len(parts) >= 2:
                province_id = int(parts[0])
                title = parts[1].strip()
                # Loại bỏ prefix "Tỉnh " hoặc "Thành phố "
                clean_title = re.sub(r'^(Tỉnh|Thành phố)\s+', '', title)
                province_data[province_id] = {
                    'id': province_id,
                    'title': title,
                    'clean_title': clean_title
                }
        
        print(f"✅ Đã load {len(province_data)} provinces")
        return province_data
    except Exception as e:
        print(f"❌ Lỗi khi đọc province: {e}")
        return {}

def parse_address_old(address_old):
    """Parse address_old để tìm ward và province"""
    result = {
        'ward_candidates': [],
        'province_candidate': None,
        'full_parts': []
    }

    # Xử lý cả dấu phẩy và dấu gạch ngang
    if ' - ' in address_old:
        # Format: "65 Vạn Bảo - Ba Đình"
        parts = [part.strip() for part in address_old.split(' - ')]
        if len(parts) >= 2:
            result['ward_candidates'].append(parts[-1])  # "Ba Đình"
    else:
        # Format: "122 Bùi Thị Xuân, Hai Bà Trưng, Hà Nội"
        parts = [part.strip() for part in address_old.split(',')]

    result['full_parts'] = parts

    # Tìm province (thường ở cuối)
    for part in reversed(parts):
        if any(keyword in part for keyword in ['Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Cần Thơ', 'Hải Phòng']):
            result['province_candidate'] = part.strip()
            break

    # Tìm ward candidates từ các phần (bỏ qua số nhà và quận/huyện)
    if ',' in address_old:  # Format có dấu phẩy
        for i, part in enumerate(parts):
            # Bỏ qua phần đầu (thường là số nhà + đường)
            if i > 0:
                # Bỏ qua các từ khóa quận/huyện/thị xã
                if not any(keyword in part.lower() for keyword in ['quận', 'huyện', 'thị xã', 'hà nội', 'hồ chí minh']):
                    result['ward_candidates'].append(part.strip())

    return result

def extract_street_info(address_old, matched_ward):
    """Trích xuất thông tin số nhà và tên đường từ address_old"""
    try:
        if ' - ' in address_old:
            # Format: "65 Vạn Bảo - Ba Đình"
            street_part = address_old.split(' - ')[0].strip()
            return street_part
        elif ',' in address_old:
            # Format: "122 Bùi Thị Xuân, Hai Bà Trưng, Hà Nội"
            parts = [part.strip() for part in address_old.split(',')]

            # Tìm phần chứa ward để biết vị trí
            ward_index = -1
            for i, part in enumerate(parts):
                if matched_ward.lower() in part.lower():
                    ward_index = i
                    break

            # Lấy tất cả phần trước ward (thường là số nhà + đường)
            if ward_index > 0:
                street_parts = parts[:ward_index]
                return ', '.join(street_parts)
            elif len(parts) > 0:
                # Nếu không tìm thấy ward, lấy phần đầu tiên
                return parts[0]

        return None
    except:
        return None

def find_matching_ward(address_old, ward_data, province_data):
    """Tìm ward phù hợp từ address_old"""
    parsed = parse_address_old(address_old)
    
    # Tìm trong ward_data
    for ward_candidate in parsed['ward_candidates']:
        if ward_candidate in ward_data:
            for ward_info in ward_data[ward_candidate]:
                province_id = ward_info['province_id']
                if province_id in province_data:
                    return {
                        'ward': ward_info,
                        'province': province_data[province_id],
                        'matched_text': ward_candidate
                    }
    
    return None

def process_brand_office_records():
    """Xử lý các record brand_office ID 2-10"""
    
    print("🔄 Xử lý brand_office records ID 2-10...")
    
    # Load dữ liệu
    ward_data = load_ward_data()
    province_data = load_province_data()
    
    if not ward_data or not province_data:
        print("❌ Không thể load dữ liệu")
        return
    
    # Lấy dữ liệu brand_office
    try:
        cmd = [
            'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
            '-D', 'urbox', '--batch', '--raw',
            '-e', 'SELECT id, address_old, city_id, title_city FROM brand_office WHERE id BETWEEN 2 AND 10'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        if result.returncode != 0:
            print(f"❌ Lỗi MySQL: {result.stderr}")
            return
        
        lines = result.stdout.strip().split('\n')[1:]  # Bỏ header
        
        update_statements = []
        
        for line in lines:
            parts = line.split('\t')
            if len(parts) >= 4:
                record_id = int(parts[0])
                address_old = parts[1] if parts[1] != 'NULL' else ''
                current_city_id = int(parts[2]) if parts[2] != 'NULL' else 0
                title_city = parts[3] if parts[3] != 'NULL' else ''
                
                print(f"\n📝 Processing ID {record_id}:")
                print(f"   Address old: {address_old}")
                print(f"   Current city_id: {current_city_id}")
                
                if address_old:
                    match = find_matching_ward(address_old, ward_data, province_data)
                    
                    if match:
                        ward_info = match['ward']
                        province_info = match['province']
                        
                        # Tạo address mới: giữ số nhà + đường + prefix + ward + province (bỏ quận/huyện)
                        street_info = extract_street_info(address_old, match['matched_text'])
                        if street_info:
                            new_address = f"{street_info}, {ward_info['prefix']} {ward_info['title']}, {province_info['title']}"
                        else:
                            new_address = f"{ward_info['prefix']} {ward_info['title']}, {province_info['title']}"
                        new_city_id = province_info['id']
                        new_ward_id = int(ward_info['id'])

                        print(f"   ✅ Match found: {match['matched_text']}")
                        print(f"   📍 New address: {new_address}")
                        print(f"   🏙️ New city_id: {new_city_id}")
                        print(f"   🏘️ New ward_id: {new_ward_id}")

                        # Tạo UPDATE statement
                        update_sql = f"""UPDATE brand_office SET
    address = '{new_address.replace("'", "\\'")}',
    city_id = {new_city_id},
    ward_id = {new_ward_id},
    updated = {int(__import__('time').time())},
    updated_by = 'address_update_script'
WHERE id = {record_id}"""
                        
                        update_statements.append(update_sql)
                    else:
                        print(f"   ❌ No match found")
                else:
                    print(f"   ⚠️ No address_old data")
        
        # Ghi ra file SQL
        if update_statements:
            with open('update_brand_office.sql', 'w', encoding='utf-8') as f:
                f.write('-- Update brand_office addresses\n\n')
                for i, stmt in enumerate(update_statements):
                    f.write(f'-- Record {i+1}\n')
                    f.write(stmt + ';\n\n')
            
            print(f"\n💾 Đã tạo {len(update_statements)} UPDATE statements trong update_brand_office.sql")
        else:
            print("\n⚠️ Không có record nào cần update")
            
    except Exception as e:
        print(f"❌ Lỗi khi xử lý: {e}")

if __name__ == "__main__":
    process_brand_office_records()
