#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import subprocess
import time

def import_tinhthanh_data():
    """Import dữ liệu từ tinhthanh.json vào bảng tinhthanh"""
    
    print("🔄 Import dữ liệu từ tinhthanh.json...")
    
    try:
        # Đọc file JSON
        with open('tinhthanh.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 Đã đọc {len(data)} records từ JSON")
        
        # Tạo các câu lệnh INSERT
        insert_statements = []
        current_time = int(time.time())
        
        for i, record in enumerate(data, 1):
            try:
                # L<PERSON>y dữ liệu từ JSON (bỏ qua các key số)
                mahc = record.get('mahc', 0)
                tentinh = record.get('tentinh', '').replace("'", "\\'")
                dientichkm2 = record.get('dientichkm2', '').replace("'", "\\'")
                dansonguoi = record.get('dansonguoi', '').replace("'", "\\'")
                trungtamhc = record.get('trungtamhc', '').replace("'", "\\'")
                kinhdo = record.get('kinhdo', 0)
                vido = record.get('vido', 0)
                truocsapnhap = record.get('truocsapnhap', '').replace("'", "\\'")
                con = record.get('con', '').replace("'", "\\'")
                
                # Tạo câu lệnh INSERT
                insert_sql = f"""INSERT INTO tinhthanh (
    mahc, tentinh, dientichkm2, dansonguoi, trungtamhc, 
    kinhdo, vido, truocsapnhap, con
) VALUES (
    {mahc}, '{tentinh}', '{dientichkm2}', '{dansonguoi}', '{trungtamhc}',
    {kinhdo}, {vido}, '{truocsapnhap}', '{con}'
)"""
                
                insert_statements.append(insert_sql)
                
                if i % 10 == 0:
                    print(f"📝 Đã tạo SQL cho {i} records...")
                    
            except Exception as e:
                print(f"❌ Lỗi tại record {i}: {e}")
                continue
        
        print(f"✅ Đã tạo {len(insert_statements)} INSERT statements")
        
        # Ghi ra file SQL backup
        with open('import_tinhthanh.sql', 'w', encoding='utf-8') as f:
            f.write('-- Import tinhthanh data from JSON\n')
            f.write('-- Clear existing data first\n')
            f.write('DELETE FROM tinhthanh;\n\n')
            
            for i, stmt in enumerate(insert_statements):
                f.write(f'-- Record {i+1}\n')
                f.write(stmt + ';\n\n')
        
        print(f"💾 Đã tạo file backup: import_tinhthanh.sql")
        
        # Thực thi import
        print("\n🔄 Thực thi import vào database...")
        
        # Xóa dữ liệu cũ trước
        cmd_clear = [
            'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
            '-D', 'urbox', '-e', 'DELETE FROM tinhthanh'
        ]
        
        result = subprocess.run(cmd_clear, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Đã xóa dữ liệu cũ")
        else:
            print(f"⚠️ Lỗi khi xóa dữ liệu cũ: {result.stderr}")
        
        # Import dữ liệu mới
        cmd_import = [
            'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
            '-D', 'urbox', '-e', f'source import_tinhthanh.sql'
        ]
        
        result = subprocess.run(cmd_import, capture_output=True, text=True, cwd='.')
        if result.returncode == 0:
            print("✅ Import thành công!")
        else:
            print(f"❌ Lỗi import: {result.stderr}")
            return False
        
        # Kiểm tra kết quả
        cmd_count = [
            'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
            '-D', 'urbox', '-e', 'SELECT COUNT(*) as total FROM tinhthanh'
        ]
        
        result = subprocess.run(cmd_count, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"📊 Kết quả: {result.stdout}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi import: {e}")
        return False

if __name__ == "__main__":
    import_tinhthanh_data()
