#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from jira_file_uploader import JiraFileUploader

def main():
    """Upload geometry crawl files to UBG-2816"""
    print("🚀 UPLOAD GEOMETRY CRAWL FILES TO JIRA")
    print("=" * 60)
    
    # Configuration
    JIRA_BASE_URL = "https://urbox.atlassian.net"
    ISSUE_KEY = "UBG-2816"
    
    # Files to upload
    geometry_files = [
        "crawl_geometry.py",
        "crawl_single_geometry.py", 
        "crawl_missing_geometry.py",
        "create_ward_geometry_table.sql",
        "sample_geometry.json"
    ]
    
    # Check which files exist
    existing_files = []
    missing_files = []
    
    for file_path in geometry_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            file_size = os.path.getsize(file_path)
            print(f"✅ Found: {file_path} ({file_size:,} bytes)")
        else:
            missing_files.append(file_path)
            print(f"❌ Missing: {file_path}")
    
    print()
    print(f"📊 Summary: {len(existing_files)} found, {len(missing_files)} missing")
    
    if not existing_files:
        print("❌ No files to upload!")
        return
    
    if missing_files:
        print(f"⚠️ Missing files: {', '.join(missing_files)}")
        print()
    
    # Get credentials
    print("🔐 Please provide your Jira credentials:")
    email = input("Enter your Jira email: ").strip()
    if not email:
        print("❌ Email is required!")
        return
    
    api_token = input("Enter your Jira API token: ").strip()
    if not api_token:
        print("❌ API token is required!")
        return
    
    print()
    print(f"🎯 Target issue: {ISSUE_KEY}")
    print(f"📋 Files to upload: {len(existing_files)}")
    
    # Confirm upload
    confirm = input("Proceed with upload? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ Upload cancelled!")
        return
    
    # Initialize uploader
    try:
        uploader = JiraFileUploader(JIRA_BASE_URL, email, api_token)
        
        # Upload files
        results = uploader.upload_multiple_files(ISSUE_KEY, existing_files)
        
        # Final summary
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful
        
        print()
        print("🎉 UPLOAD COMPLETED!")
        print(f"✅ Successful uploads: {successful}")
        print(f"❌ Failed uploads: {failed}")
        
        if failed > 0:
            print("\n❌ Failed files:")
            for result in results:
                if not result['success']:
                    print(f"  - {os.path.basename(result['file_path'])}")
        
        print(f"\n🔗 View issue: {JIRA_BASE_URL}/browse/{ISSUE_KEY}")
        
    except Exception as e:
        print(f"❌ Error during upload: {e}")
        return

if __name__ == "__main__":
    main()
